/*
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>@class30.com
 * @Date: 2024-03-14 20:23:44
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-07-23 11:54:18
 */
/**
 * @name: 试卷来源类别
 */
export enum ISOURCETYPE {
  /** 个册 */
  GC = "1",
  /** 大精考阅试卷 */
  PAPER = "2",
  /** 手阅作业/个性化练习册 */
  HW = "3",
  /** 作业校本试卷 */
  SCHOOL = "4",
  /** 大精手动组卷(大精校本题库) */
  HAND = "5",
  /** web题库组卷 */
  WEB = "12",
}

/* 编辑权限类型 */
export enum IEditerPowerType {
  /** 普通卡 */
  NORMAL = "1",
  /** 模版卡无权限 */
  TPL_NO_POWER = "2",
  /** 模版卡有权限 */
  TPL_HAS_POWER = "3",
}

/**
 * @name: 考号类型
 */
// 1.准考证号 2.条形码
export enum INUMBERLAYOUT {
  TICKET = 1,
  BARCODE = 2,
  WRITE = 3,
  QRCODE = 4,
  MORE = 5,
}

export enum QUES_SCAN_MODE {
  /** 普通  */
  NORMAL = 0,
  /** 智能批改 */
  AI_FILL = 1,
  /** 智能批改-简答 */
  AI_SUBJECT = 2,
  /** 智能批改-作文 */
  AI_ESSAY = 3,
}

/**
 * @name: 题目类型
 */
export enum QUES_TYPE {
  /** 单选 */
  singleChoice = 8,
  /** 多选题 */
  choice = 1,
  /** 判断题 */
  judge = 2,
  /** 填空题 */
  fill = 3,
  /** 语音题 */
  voice = 4,
  /** 英语自动评测题 */
  evaluating = 5,
  /** 简答题 */
  subject = 6,
  /** 填空题自动批改 */
  fillEva = 7,
  /* 混合题 */
  mixin = 9,
  /* 非作答区 */
  noAnswerArea = 10,
}

/**
 * @name: 简答题的不同模式
 */
export enum SUBJECT_MODE {
  // 普通
  normal = 1,
  // 作文
  writing = 2,
  // 卷面分
  face = 3,
}

/**
 * @name: 判断题类型
 */
export enum JUDGE_TYPE {
  /** √/x */
  MARK = 1,
  /** T/F */
  TF = 2,
  /** A/B */
  AB = 3,
}
/**
 * @name: 作答类型
 */
export enum ANSWER_TYPE {
  /** 填涂 */
  fill = 1,
  /** 手写 */
  write = 2
}
/**
 * @name: 判分格位置
 */
export enum GRIDPLACE_TYPE {
  /** 题干前 */
  FRONT = 1,
  /** 题干后 */
  AFTER = 2,
}
/**
 * @name: 打分模式
 */
export enum MARK_TYPE {
  /** 分数格 */
  NUMBER = 1,
  /** 对错 */
  YESORNO = 2,
  /** 仅标识错误 */
  ONLYNO = 3,
  /** 手写打分 */
  WRITE = 4,
  /** 线上批改 */
  ONLINE = 5,
}
/**
 * @name: 十个位分开
 */
export enum GRID_TYPE {
  /** 是 */
  YES = 1,
  /** 否 */
  NO = 2,
}
/**
 * @name: 分数精度 decimal
 */
export enum DECIMAL_TYPE {
  /** 有小数 */
  HAVE = 1,
  /** 无小数 */
  NOTHAVE = 2,
}
/**
 * @name: 批阅类型
 */
export enum ICorrectType {
  HAND = 1,
  WEB = 2,
  /** 拍改（移动端拍扫改卷） */
  PHOTO = 3,
}
/**
 * @name: 排列方式
 */
export enum ARRANGE_TYPE {
  //横排
  HORIZONTAL = 1,
  //竖排
  VERTICAL = 2,
}
/**
 * @name: 划线长度
 */
export enum LINE_WIDTH {
  //二分之一
  HALF = 2,
  //三分之一
  THIRD = 3,
  //四分之一
  QUARTER = 4,
  //五分之一
  FIFTH = 5,
  //整行
  ENTIRE = 1,
}
/**
 * @name: 学科模型
 */
export interface ISubject {
  subjectId: string;
  subjectName: string;
}
/**
 * @name: 页面布局
 */
export enum IPAGELAYOUT {
  A4 = 1,
  A42 = 6,
  A3 = 2,
  A33 = 3,
  A32 = 4,
  A23 = 5
}
/**
 * @name: 制卡模式
 */
// 0：纯答题卡 1：题卡合一 2：仅答题卡 3:三方卡 4:英语专项
export enum ICARDMODEL {
  BLANKCARD = 0,
  QUESCARD = 1,
  ONLYCARD = 2,
  ENGLISH = 4,
  PHOTO = 5
}
/**
 * @name: 半对给分规则
 * 0 标准 1 新高考 2 自定义
*/
export enum IRULERTYPE {
  STANDAD = 0,
  EXAM = 1,
  CUSTOM = 2,
  BREAK = 3
}
/**
 * @name: 字号、行距、字体模型
 */
export interface IBaseModel {
  id: number;
  text: string;
  value: string;
}

export interface LineModel {
  index: number;
  width: string
  editContent: string
  id: string
  isLineFeed: boolean
  score: string | number
  answer?: string
}

/**
 * @name: 大题模型
 */
export interface IBigQues {
  //题目id
  id: string;
  //名称
  name: string;
  fullname?: string;
  //类别
  typeId: QUES_TYPE;
  //类别名称
  type: string;
  // 同题型的不同模式
  // TODO 当前为拓展预留字段，后期如果类型复杂上升可以考虑全部启动
  typeMode?: SUBJECT_MODE;
  // 是否包含卷面分
  hasFaceScore?: boolean;
  /**
   * @description: 填空题行高（废弃）
   * @deprecated
   */
  fillLineHeight?: any;
  // 填空题间距
  fillDistance?: number
  //每题分数
  quesScore: any;
  //小题个数
  count: number;
  //总分
  score: number;
  // 小题集合
  data: ISmallQues[];
  //选项个数
  optionCount?: number;
  //是否为作文题
  isWriting?: boolean;
  //半对得分
  halfScore?: number;
  //多选题给分类型
  ruleType?: 0,
  //多选题赋分规则
  rules?: Array<any>,
  //判断题样式
  judgeType?: JUDGE_TYPE;
  //每题步进
  step?: number;
  //判分格位置
  gridPlace?: GRIDPLACE_TYPE;
  //打分模式
  markType?: MARK_TYPE;
  //十个位分开
  gridType?: GRID_TYPE;
  //是否有小数点
  decimal?: DECIMAL_TYPE;
  lineType?: LINE_WIDTH;
  lineNum?: number;
  lineList?: LineModel[];
  // 简答题是否有下划线
  hasLine?: boolean;
  showName?: number;
  //作文题字数
  wordNumber?: number;
  //作文格大小
  cellSize?: string;
  //单元格数量
  cells?: number;
  //行数
  rowNum?: number;
  // 父级id:如果混合题会嵌套在数据中
  parentId?: string;
  // 混合模式，除了混合题，单题也可以加入混合模式，支持混合特性
  mixinMode?: boolean;
  // 是否拆分单独大题
  isSplited?: boolean;
  // 答题高度,mm
  height?: string;
  // 选择题选项排列方式
  showType?: "horizontal" | "vertical";
  // 竖排每组显示行数，默认为5
  verticalLines?: number;
  // 选择题独立成组
  groupAlone?: "0" | "1";
  // 是否隐藏题干
  hideQuesSurface?: boolean;
  // 是否折叠
  collapsed?: boolean
  quesName?: string
  childIndex?: number
  key?: any
  children?: ISmallQues[]
  doQuesList?: any[]
  scanMode?: QUES_SCAN_MODE
}

/**
 * @name: 小题模型
 */
export interface ISmallQues {
  //id
  id: string;
  //名称
  name: string;
  //选项个数
  optionCount: number;
  //题号
  quesNos: string;
  //分数
  score: number;
  //答案
  answer: string | string[];
  //题型名称
  type: string;
  //题型id
  typeId: number;
  isSplited?: boolean;
  parentId?: string;
  //半对得分
  halfScore?: number;
  //多选题给分类型
  ruleType?: 0;
  //多选题赋分规则
  rules?: Array<any>;
  //判断题样式
  judgeType?: JUDGE_TYPE;
  //每题步进
  step?: number;
  //判分格位置
  gridPlace?: GRIDPLACE_TYPE;
  //打分模式
  markType?: MARK_TYPE;
  //十个位分开
  gridType?: GRID_TYPE;
  //是否有小数点
  decimal?: DECIMAL_TYPE;
  // 简答题是否有下划线
  hasLine?: boolean;
  //是否为作文题
  isWriting: boolean;
  //作文题字数
  wordNumber?: number;
  //作文格大小
  cellSize?: string;
  //单元格数量
  cells?: number;
  //行数
  rowNum?: number;
  //空格数
  lineNum?: number;
  lineWidth?: string;
  lineList?: Array<any>;
  lineType?: LINE_WIDTH;
  editContent?: string;
  rendered?: boolean;
  // 选择题独立成组
  groupAlone?: "0" | "1";
  quesName?: string
  maxLevel?: number
  data?: any[]
  children?: any[]
  mixinMode?: boolean
  scanMode?: QUES_SCAN_MODE
}

/**
 * @name: 试卷模型
 */
export interface IPaperState {
  //试卷id
  tbId: string;
  //试卷名称
  name: string;
  //年份
  year: number;
  //年级id
  gradeId: string;
  //学科id
  subjectId: string;
  //章节id
  categoryId: string;
  //用户id
  userId: string;
  //学校id
  schoolId: string;
  //类别
  shapeType: string;
  //题目json数据
  quesInfo: string;
  //页面布局
  layout: string;
  //制卡模式
  cardType: ICARDMODEL;
  //字号
  fontSize: string;
  //行距
  space: string;
  //字体
  fontFamily: string;
}
/** 班级类型 */
export const CLASS_TYPE = {
  // 备课
  lessons: 1,
  // 授课
  substitute: 2,
};
/** 题目批改类型*/
export const CORRECTING_TYPE = {
  // 二维码
  QRCODE: 1,
  // 考号
  EXAMNUMBER: 2,
  //填涂题
  FILLING: 3,
  // 划线题
  LINE: 4,
  //注意事项
  NOTICE: 5,
  //条形码
  BARCODE: 6,
  // 智能批改
  LINE_EVA: 7,
  // 缺考标记
  MISS_EXAM: 8,
};

//卡类型
export const IAB_CARD_TYPE = {
  /*普通卡*/
  default: 0,
  /*AB卡*/
  abCard: 1,
  /*AB卷*/
  abPaper: 2,
}
//关联答题卡类型
export const ICARD_STATE = {
  /*普通卡*/
  default: 0,
  /*AB卡*/
  abCard: 1,
  /*AB卷*/
  abPaper: 2,
  /*AB卷(2张卡)*/
  abPaperTwo: 3,
}
// AB卡或者AB卷时,此字段生效 0:A卡(卷) 1:B卡(卷)
export const IAB_CARD_SHEEFT_TYPE = {
  /*A卡*/
  aCard: 0,
  /*B卡*/
  bCard: 1,
}

/* 拆分的节点类型 */
export interface SplitNodes {
  id: string;
  type: "ques" | "blank";
  pageSize: number;
  height: string;
  width?: string;
  tagIndex?: string;
  splitDoms?: HTMLElement;
  data?: any;
  editContent?: string;
}
