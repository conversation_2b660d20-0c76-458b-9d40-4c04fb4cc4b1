<!--
 * @Description: 预览页面
 * @Author: <PERSON><PERSON><PERSON><PERSON>@class30.com
 * @Date: 2024-03-25 10:40:19
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-04-23 10:02:33
-->
<template>
  <div id="previewCont" :class="[
    'preview',
    Paper.cardType == ICARDMODEL.QUESCARD ? 'page-box--ques' : 'page-box--blank',
    [IPAGELAYOUT.A4,IPAGELAYOUT.A42].includes(Paper.pageLayout) ? 'a4' : (Paper.pageLayout === IPAGELAYOUT.A3 ? 'a3' : 'a33')
  ]" :style="{
  '--fontSize': Paper.fontSize,
  '--fontFamily': Paper.fontFamily,
  '--lineHeight': Paper.space,

  'zoom': editZoom,
  'transform': `scale(${1 / editZoom}) translateX(-50%)`,
  'transform-origin': '0 0',
  'position': 'absolute',
  'left': '50%',
}">
    <div class="preview-warp" v-for="page in pageCount" :key="page">
      <div class="preview-header" style="height: 13mm;" v-if="!isPhotoCorrect">
        <div class="locatePoint-l"></div>
        <div class="locatePoint-r"></div>
        <span class="styles_left styles_idAndNameBase">
          编号：{{ Paper.paperId }}
        </span>
        <span class="custom-name"
          style="left: 0;text-align: center;right: 0;position: absolute;bottom: 1mm;font-size: 3.2mm;font-family: 宋体;">
          {{ Paper.headerName }}
        </span>
      </div>
      <div class="preview-content" :class="{ 'text-center': isPhotoCorrect }">
        <template v-for="sf in step" :key="sf">
          <div v-if="hasSurface(sf, page)" class="surface-warp"></div>
          <div v-else-if="hasWidthSurface(sf, page)" class="surface-warp l_width"></div>
          <div v-if="hasLine(sf, page)" class="line"></div>
          <div v-else-if="hasBoldLine(sf, page)" class="bold line"></div>
        </template>
      </div>
      <div class="preview-footer" v-if="!isPhotoCorrect">
        <div class="locatePoint-l"></div>
        <div class="locatePoint-r"></div>
        <span class="page">第{{ page }}页</span><span class="total">(共{{ pageCount }}页)</span>
      </div>
      <div v-if="page % 2 != 0" class="hand-write-div"
        :style='{ "position": "absolute", "left": "-14mm", "top": "14mm" }'>
        <hand-write-area></hand-write-area>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted, onBeforeMount, onBeforeUnmount, computed } from 'vue';
import Paper from '@/views/paper';
import { IPAGELAYOUT, ICARDMODEL, ICorrectType } from '@/typings/card';
import { getBrowerZoom } from '@/utils/util';
import HandWriteArea from '@/components/HandWriteArea.vue';
import { mmConversionPx } from '@/utils/dom';
import { footerKeepHeight } from '@/utils/config';

export default defineComponent({
  components: {
    HandWriteArea,
  },
  setup() {
    const step =
    [IPAGELAYOUT.A4,IPAGELAYOUT.A42].includes(Paper.pageLayout) ? 1 : Paper.pageLayout == IPAGELAYOUT.A3 ? 2 : 3;
    // 每张纸的分栏数量
    const paperColCount = [IPAGELAYOUT.A23, IPAGELAYOUT.A32].includes(Number(Paper.pageLayout))
      ? 5
      : step * 2;
    const state = reactive({
      editZoom: window.EDIT_ZOOM,
      Paper: Paper,
      surfaceCount: 1,
      // 纸张页数
      pageCount: 1,
      // 纸张张数
      paperCount: 1,
      step,
      IPAGELAYOUT: IPAGELAYOUT,
      ICARDMODEL: ICARDMODEL,
      zoom: getBrowerZoom(),
    });
    // 是否拍改类型
    const isPhotoCorrect = computed(() => {
      return Paper.correctType == ICorrectType.PHOTO;
    })

    onBeforeMount(() => {
      state.surfaceCount = document.getElementsByClassName('header-box').length;
      state.pageCount = Math.ceil(state.surfaceCount / state.step);
      state.paperCount = Math.ceil(state.pageCount / 2);
    });

    onBeforeUnmount(() => {
      // 防止预览残留
      let surfaceWarps = document.getElementsByClassName('surface-warp');
      Array.from(surfaceWarps).forEach($el =>
        Array.from($el.children).forEach($subEl => $subEl.remove())
      );
    });

    const hasSurface = (index, page) => {
      if (Paper.pageLayout == IPAGELAYOUT.A33) {
        return true;
      }
      if (Paper.pageLayout == IPAGELAYOUT.A3 || [IPAGELAYOUT.A4,IPAGELAYOUT.A42].includes(Paper.pageLayout)) {
        return false;
      }
      const layoutConfigs = {
        [IPAGELAYOUT.A32]: {
          first: [1, 2, 3],
          last: [],
        },
        [IPAGELAYOUT.A23]: {
          first: [],
          last: [1, 2, 3],
        },
      };

      const { first, last } = layoutConfigs[Paper.pageLayout];
      return (page % 2 === 0 ? last : first).includes(index);
    };

    const hasWidthSurface = (index, page) => {
      if (Paper.pageLayout == IPAGELAYOUT.A33) {
        return false;
      }
      if ([IPAGELAYOUT.A4,IPAGELAYOUT.A42].includes(Paper.pageLayout) || Paper.pageLayout == IPAGELAYOUT.A3) {
        return true;
      }
      const layoutConfigs = {
        [IPAGELAYOUT.A32]: {
          first: [],
          last: [1, 2],
        },
        [IPAGELAYOUT.A23]: {
          first: [1, 2],
          last: [],
        },
      };

      const { first, last } = layoutConfigs[Paper.pageLayout];
      return (page % 2 === 0 ? last : first).includes(index);
    };

    const hasLine = (index, page) => {
      const layouts = {
        [IPAGELAYOUT.A33]: [1, 2],
        [IPAGELAYOUT.A32]: page % 2 ? [1, 2] : [],
        [IPAGELAYOUT.A23]: page % 2 ? [] : [1, 2],
      };
      const targetLayout = layouts[Paper.pageLayout];
      return targetLayout && targetLayout.includes(index);
    };

    const hasBoldLine = (index, page) => {
      const layouts = {
        [IPAGELAYOUT.A3]: [1],
        [IPAGELAYOUT.A32]: page % 2 ? [] : [1],
        [IPAGELAYOUT.A23]: page % 2 ? [1] : [],
      };
      const targetLayout = layouts[Paper.pageLayout];
      return targetLayout && targetLayout.includes(index);
    };

    // 判断两个数据是否有交集
    const hasIntersection = (arr1, arr2) => arr1.some(Set.prototype.has, new Set(arr2));

    const copyNode = () => {
      const el = document.getElementsByClassName('page-box')[0];
      const children = el.children;
      let headerCount = 1;
      let index = -1;
      
      for (let i = 0; i < children.length; i++) {
        if (children[i].className.includes('hand-write-div')) {
          continue;
        }
        // 计数splitor节点来判断换页面
        let surfaceWarps = document.getElementsByClassName('surface-warp');
        let $surfaceWarp = null;
        let isSplitor =
          (children[i].classList.contains('header-box') &&
            !children[i].classList.contains('absolute')) ||
          children[i].classList.contains('splitor-empty');

        if (isSplitor) {
          index++;
          $surfaceWarp = surfaceWarps[index];

          if (!$surfaceWarp) continue;
          $surfaceWarp.innerHTML = '';
        }

        $surfaceWarp = surfaceWarps[index];
        if (!$surfaceWarp) continue;

        if (
          hasIntersection(
            ['header-box', 'splitor-empty', 'footer', 'page-line'],
            children[i].classList
          )
        )
          continue;

        let quesCard: any = children[i].cloneNode(true);
        let isFillCard = quesCard.className.includes('card--fill');
        let fiilImages = [];
        if (isFillCard) {
          // 填空题判断图片位置,拆分出不同组的图片
          let $imglistContainer = quesCard.querySelectorAll('.imglist-content');
          if ($imglistContainer) {
            let $imgsContainer = $imglistContainer[0];

            if ($imgsContainer.childElementCount) {
              Array.from($imgsContainer.children).forEach(($img: any) => {
                fiilImages.push($img.cloneNode(true));
              });
            }
          }
        }

        if (quesCard.className.includes('q-opt')) {
          let splitors: HTMLElement[] = Array.from(quesCard.querySelectorAll('.page-splitor'));
          if (splitors.length) {
            let $parent = splitors[0].parentElement;
            let childLength = $parent.children.length;
            let $score = Array.from($parent.children).find(item =>
              item.className.includes('score-container')
            );
            let splitChildren = Array.from($parent.children);

            splitChildren.forEach(($el: HTMLElement, elIndex: number) => {
              let isScoreNameEl =
                $el.className.includes('score-container') ||
                $el.className.includes('ques-content-name');
              if ($el.className.includes('page-splitor') || isScoreNameEl) {
                // 不参与计算的元素减少总数量
                if (isScoreNameEl) childLength--;
                return;
              }

              let quesCardClone: any = children[i].cloneNode(true);
              let splitors: HTMLElement[] = Array.from(
                quesCardClone.querySelectorAll('.page-splitor')
              );
              let $parentClone = splitors[0].parentElement;
              $parentClone.innerHTML = '';

              // 检测是否需要插入分数
              if ($score) {
                let isAfter = $score.className.includes('after');
                if (isAfter ? elIndex >= childLength : elIndex === 1) {
                  $parentClone.appendChild($score);
                }
              }

              $parentClone.appendChild($el);
              $surfaceWarp.appendChild(quesCardClone);

              // 填空题分摊图片到不同的分段
              if (isFillCard) {
                let $imglistContainer = quesCardClone.querySelectorAll('.imglist-content');
                if ($imglistContainer) {
                  let $imgsContainer = $imglistContainer[0];
                  $imgsContainer.innerHTML = '';
                }
                if (fiilImages.length) {
                  let elHeight = $el.offsetHeight;
                  if ($imglistContainer) {
                    let $imgsContainer = $imglistContainer[0];
                    fiilImages = fiilImages.filter(img => {
                      let imgH = img.offsetHeight;
                      let imgTop = Number(img.style.top.replace('px', ''));

                      if (imgTop < elHeight - imgH / 2) {
                        $imgsContainer.appendChild(img);
                        return false;
                      } else {
                        let $names = quesCardClone.querySelectorAll('.ques-content-name');
                        imgTop = imgTop - elHeight - mmConversionPx(footerKeepHeight * 2);
                        if (elIndex === 0 && $names.length) {
                          img.style.top = imgTop - $names[0].offsetHeight + 'px';
                        } else {
                          img.style.top = imgTop + 'px';
                        }
                        return true;
                      }
                    });
                  }
                }
              }

              let spIndex = Number($el.dataset.index);
              // 只保留最后一个拆分线
              let splitLines = quesCardClone.querySelectorAll('.split-line--subject');
              if (splitLines.length && elIndex <= $parent.children.length) {
                splitLines[0].remove();
              }

              if (spIndex > 0) {
                // 清除第一个分段之外的标题
                let $nameEle = quesCardClone.querySelectorAll('.ques-content-name');
                if (!$nameEle.length) {
                  $nameEle = quesCardClone.querySelectorAll('.ques-content-name--small');
                }
                if ($nameEle.length) $nameEle[0].remove();
              }
              if (elIndex < childLength - 1) {
                index++;
                $surfaceWarp = surfaceWarps[index];
              }
            });

            continue;
          }
        }

        $surfaceWarp.appendChild(quesCard);
        if (quesCard.className.includes('header-info absolute')) {
          // 新纸张的头部为绝对定位，这里需要将位置调换到前排
          quesCard.style.removeProperty('top');
          quesCard.style.removeProperty('left');

          $surfaceWarp = surfaceWarps[paperColCount * headerCount];
          let firstChild = $surfaceWarp.firstElementChild;
          if (firstChild) {
            $surfaceWarp.insertBefore(quesCard, firstChild);
          } else {
            $surfaceWarp.appendChild(quesCard);
          }
          headerCount++;
        }
      }
    };

    onMounted(async () => {
      copyNode();
    });

    return {
      ...toRefs(state),
      isPhotoCorrect,
      hasSurface,
      hasWidthSurface,
      hasLine,
      hasBoldLine,
    };
  },
});
</script>

<style lang="scss" scoped>
.preview {
  min-height: 297mm;
  margin: 0 auto;
  // padding: 0 2mm;
  background: #fff;
  pointer-events: none;
  font-size: var(--fontSize);
  font-family: var(--fontFamily);
  line-height: var(--lineHeight);

  &.a4 {
    width: 210mm;

    .preview-warp {
      width: 192mm;

      .surface-warp {
        width: 192mm;
      }
    }
  }

  &.a3 {
    width: 420mm;

    .preview-warp {
      width: 392mm;

      .surface-warp {
        width: 192mm;
      }
    }
  }

  &.a33 {
    width: 420mm;

    .preview-warp {
      width: 398mm;
    }

    .surface-warp {
      width: 130mm !important;

      &.l_width {
        width: 192mm !important;
      }
    }

    .line {
      width: 4mm !important;

      &.bold {
        width: 14mm !important;
      }
    }
  }

  .preview-warp {
    height: 297mm;
    // border-bottom: 0.3mm solid #000;
    margin: 0 auto;
    position: relative;

    .preview-header {
      padding: 0;
      margin: 0px 0px 1mm;
      position: relative;
      width: 100%;
      height: 14mm;
      border-bottom: 0.1mm solid rgb(0, 0, 0);
      font-family: 宋体, SimSun, Times New Roman;

      .locatePoint-l {
        top: 3mm;
        left: -7mm;
        width: 7mm;
        height: 3mm;
        position: absolute;
        background-color: black;
        display: block;
      }

      .locatePoint-r {
        top: 3mm;
        right: -7mm;
        width: 7mm;
        height: 3mm;
        position: absolute;
        background-color: black;
        display: block;
      }

      .styles_left {
        left: 0;
      }

      .styles_right {
        position: absolute;
        bottom: 0;
        padding: 0;
        margin: 0;
        font-size: 3.8mm;
        overflow: hidden;
        right: 0;
        min-width: 8mm;
      }

      .styles_idAndNameBase {
        position: absolute;
        bottom: 1mm;
        padding: 0;
        margin: 0;
        font-size: 3.8mm;
        overflow: hidden;
        color: #3d3d3d;
      }
    }

    .preview-content {
      height: 269mm;

      .surface-warp {
        // width: 192mm;
        display: inline-block;
        // padding: 0 2mm;
        vertical-align: top;
      }
    }

    .preview-footer {
      text-align: center;
      color: #000;
      display: flex;
      height: 14mm;
      width: 100%;
      justify-content: center;
      align-items: center;
      position: relative;

      .locatePoint-l {
        top: 1mm;
        left: -7mm;
        width: 3mm;
        height: 7mm;
        position: absolute;
        background-color: black;
        display: block;
      }

      .locatePoint-r {
        top: 1mm;
        right: -7mm;
        width: 3mm;
        height: 7mm;
        position: absolute;
        background-color: black;
        display: block;
      }

      .page-tag {
        // width: 78px;
        // height: 23px;
        // line-height: 23px;
        text-align: center;
        background: #d8d8d8;
        opacity: 1;
        margin-left: 3mm;
        padding: 1mm 2mm;
        border-radius: 4px 4px 4px 4px;
        font-size: 2.8mm;
      }
    }

    .line {
      height: 100%;
      width: 8mm;
      display: inline-block;
      background-color: transparent;
    }
  }

  ::v-deep {
    .split-line {
      position: relative;
      display: block;
      padding: 1mm 0;
      cursor: pointer;
    }

    .split-line.split-line--subject {
      position: absolute;
      margin: -1mm 0;
      z-index: 100;
      left: 0;
      right: 0;
      box-sizing: border-box;
      border-left: 0.1mm solid;
      border-right: 0.1mm solid;
      display: none;
    }

    .split-line.split-line--subject.hide-block {
      background-color: #fff;
      display: block;
    }

    .line-list {
      p {
        line-height: normal;
      }
    }
  }
}
</style>
