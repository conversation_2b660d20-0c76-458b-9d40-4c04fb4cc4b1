/*
 * @Descripttion: 本地上传dist部署测、现网脚本
 * @Author: 小圆
 * @Date: 2024-01-02 19:17:18
 * @LastEditors: 小圆
 */
const path = require('path');
const fs = require('fs');
const { NodeSSH } = require('node-ssh'); // NPM node-ssh，根据ssh2封装的Promise库
const Client = require('ssh2-sftp-client'); // NPM ssh下载客户端库
const archiver = require('archiver'); // NPM archiver，压缩包操作库
const moment = require('moment');
// const readline = require('readline');

const devConfig = require('./dev.json');

const __projectDir = process.cwd();

let ssh = new NodeSSH(); // 生成ssh对象，用于操作服务器
const sftp = new Client(); // 生成sftp文件传输服务对象，用于下载文件
const argv = process.argv.splice(2)[0];

let config;

// 主函数
(async function () {
  // const rl = readline.createInterface({
  //   input: process.stdin,
  //   output: process.stdout
  // });
  // rl.question("是否将dist目录部署到测网zy目录(y/n)：", function (answer) {
  //   rl.close();
  //   if (answer === 'y' || answer === 'Y') {
  //     config = devConfig;
  //     deploy();
  //     return;
  //   }
  //   console.log("取消部署");
  //   process.exit(0);
  // });
  config = devConfig;
  deploy();
})();

// 部署方法
async function deploy() {
  try {
    await startZip("dist", "dist");
    await connectSSH();
    await uploadFile();
    await clearWebDir();
    await unzipFile();
    await deleteLocalZip();
    ssh.dispose();
    process.exit(0);
  } catch (error) {
    console.error(error);
    ssh.dispose();
    process.exit(1);
  }
}

// 备份方法
async function backup() {
  try {
    const backupDir = "backup";
    const localDirectory = path.resolve(__projectDir, backupDir);
    const backFileName = "bigdata_" + moment().format("YYYY_MM_DD");
    await connectSSH();
    await connectSFTP();
    await downloadDirectory(localDirectory, config.webDir);
    await startZip(backupDir, backFileName);
    fs.rmdirSync(localDirectory, {
      recursive: true,
    });
    ssh.dispose();
    process.exit(0);
  } catch (err) {
    ssh.dispose();
    process.exit(1);
  }
}
/**
 * @description: 第一步，打包zip
 */
async function startZip(zipPath, zipName) {
  return new Promise(async (resolve, reject) => {
    zipPath = path.resolve(__projectDir, zipPath);
    if (!fs.existsSync(zipPath)) {
      console.log(zipPath, "不存在");
      reject();
      return;
    }
    console.log(`1.即将打包目录${zipPath}`);
    const archive = archiver("zip", {
      zlib: { level: 9 },
      forceLocalTime: true, // true 表示使用本地时间戳，false 表示使用 UTC 时间戳
    }).on("error", function (err) {
      throw err;
    });
    const zip = `${__projectDir}/${zipName}.zip`;
    const output = fs.createWriteStream(zip);
    output.on("close", (err) => {
      if (err) {
        reject(err);
        process.exit(1);
      }
      console.log("打包成功");
      resolve(zip);
    });
    archive.pipe(output);
    archive.directory(zipPath, "/");
    archive.finalize();
  });
}

/**
 * @description: 第二步，连接SSH
 */
async function connectSSH() {
  const { host, port, username, password } = config;
  const sshConfig = {
    host,
    port,
    username,
    password,
  };
  try {
    console.log(`2.连接${host}`);

    await ssh.connect(sshConfig);

    console.log("连接成功");
  } catch (err) {
    console.log("连接失败", err);
    process.exit(1);
  }
}

/**
 * @description: 第三步，上传zip包
 */
async function uploadFile() {
  try {
    console.log(`3.上传zip至服务器目录${config.webDir}`);

    await ssh.putFile(`${__projectDir}/dist.zip`, `${config.webDir}dist.zip`);

    console.log("zip包上传成功");
  } catch (err) {
    console.log("zip包上传失败");
    process.exit(1);
  }
}

// 清空服务器目录
async function clearWebDir() {
  console.log(`正在清空服务器目录${config.webDir}`);
  const command_1 = `cd ${config.webDir}`;
  const command_2 = `find . -maxdepth 1 ! -name "dist.zip" -exec rm -rf {} \\;`;
  await ssh.execCommand(`${command_1} && ${command_2}`);
}

/**
 * @description: 第四步，解压zip包
 */
async function unzipFile() {
  try {
    console.log(`4.开始解压服务器zip包${config.webDir}`);

    const command_1 = `cd ${config.webDir}`;
    const command_2 = `unzip -o dist.zip`;
    const command_3 = `rm -f dist.zip`;
    await ssh.execCommand(`${command_1} && ${command_2}  && ${command_3}`);
    console.log("解压成功");
  } catch (err) {
    process.exit(1);
  }
}

/**
 * @description: 第五步，删除本地dist.zip包
 */
async function deleteLocalZip() {
  return new Promise((resolve, reject) => {
    console.log("5.开始删除本地zip包");
    fs.unlink(`${__projectDir}/dist.zip`, (err) => {
      if (err) {
        console.log(`  本地zip包删除失败 ${err}`, err);
        reject(err);
        process.exit(1);
      }
      console.log("  本地zip包删除成功\n");
      resolve();
    });
  });
}

/**
 * @description: 连接SFTP
 */
async function connectSFTP() {
  const { host, port, username, password } = config;
  const sshConfig = {
    host,
    port,
    username,
    password,
  };
  try {
    console.log(`正在连接${host}下载服务`);

    await sftp.connect(sshConfig);

    console.log("连接成功");
  } catch (err) {
    console.log("连接失败", err);
    process.exit(1);
  }
}

/**
 * @description: 下载服务器文件夹文件到本地
 * @param {*} localDirectory    本地文件夹
 * @param {*} remoteDirectory    服务器文件夹
 */
async function downloadDirectory(localDirectory, remoteDirectory) {
  try {
    console.log("开始下载文件");

    if (!fs.existsSync(localDirectory)) {
      fs.mkdirSync(localDirectory, { recursive: true });
    }
    const remoteFiles = await sftp.list(remoteDirectory);
    for (const item of remoteFiles) {
      const remotePath = remoteDirectory + item.name;
      const localPath = path.join(localDirectory, item.name);

      if (item.type === "d") {
        await sftp.downloadDir(remotePath, localPath);
      } else {
        await sftp.fastGet(remotePath, localPath);
      }
    }

    console.log("文件下载成功。");
  } catch (err) {
    fs.rmdirSync(localDirectory, {
      recursive: true,
    });
    console.log("文件下载失败");
    process.exit(1);
  }
}
