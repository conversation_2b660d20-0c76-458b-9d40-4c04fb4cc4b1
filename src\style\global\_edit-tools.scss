.q-opt {
    .pop {
        display: none;
        position: absolute;
        z-index: 999;

        &.down {
            right: 0;
            bottom: -15px;
            line-height: normal;
        }

        i.el-icon-sort {
            cursor: ns-resize;
        }

        .pop-icon {
            margin-right: 5px;
            display: inline-block;
            border: 1px solid #dcdcdc;
            border-radius: 13px;
            cursor: pointer;
            background: #f0f0f0;

            &.pop-icon-free {
                cursor: n-resize;

                &:active {
                    background: #85bffa;

                    span {
                        opacity: 0.5;
                    }
                }
            }

            .half {
                background: url("../../assets/half.png") no-repeat center center;

                &:hover,
                &:active {
                    background: url("../../assets/half-hover.png") no-repeat center center;
                }
            }

            .bottom {
                background: url("../../assets/full.png") no-repeat center center;

                &:hover,
                &:active {
                    background: url("../../assets/full-hover.png") no-repeat center center;
                }
            }

            .free {
                background: url("../../assets/free.png") no-repeat center center;

                &:hover,
                &:active {
                    background: url("../../assets/free-hover.png") no-repeat center center;
                }
            }

            span {
                width: 24px;
                height: 24px;
                display: block;
            }

            &:hover {
                background: #409eff;
                border: 1px solid #409eff;
            }
        }
    }

    .edit-button-tools {
        position: absolute;
        right: 0;
        top: 0;
        z-index: 999;
        white-space: nowrap;
        // background-color: #fff;
        display: none;

        .btn-lineheight {
            display: inline-block;
            vertical-align: top;
            margin-right: 25px;

            >.label {
                margin-right: 5px;
            }
        }

        .add-picture {
            display: inline-block;
            width: 24px;
            height: 24px;
            vertical-align: top;
            font-size: 20px;
            color: rgb(128, 128, 128);
            margin-top: 2px;
            margin-right: 15px;
        }

        .edit {
            display: inline-block !important;
            width: 24px;
            height: 24px;
            background: url("../../assets/icon_bianji.png") 100% 100%;
            z-index: 2;
            margin-left: 15px;
        }

        .split {
            display: inline-block;
            width: 24px;
            height: 24px;

            background: {
                image: url("../../assets/half.png");
                position: center center;
                repeat: no-repeat;
            }
        }

        .btn-change-coltype {
            vertical-align: top;
            position: relative;
            margin-left: 10px;

            ::v-deep .el-radio-button__inner {
                padding: 5px;
            }
        }

        .btn-change-linenum {
            display: inline-block;
            vertical-align: top;
            width: 105px;
            line-height: 21px;

            ::v-deep .el-input__inner {
                height: 23px !important;
            }
        }
    }
}