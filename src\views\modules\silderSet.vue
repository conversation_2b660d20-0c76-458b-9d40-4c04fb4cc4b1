<!--
 * @Descripttion: 
 * @Author: Doudou
 * @Date: 2023-10-16 20:58:58
 * @LastEditors: liuyue <EMAIL>
-->
<template>
  <div class="c30-teach-card-silder-container">
    <div class="c30-teach-card-silder-main">
      <!-- 批阅类型 -->
      <div class="silder-title-bar" @dblclick="isShowReset = true">
        批阅类型
        <i class="dotbot"></i>
      </div>
      <div class="silder-content">
        <el-radio-group v-model="Paper.correctType" @change="correctChange" :disabled="Paper.isEdit || isPhotoCorrect">
          <el-radio :label="ICorrectType.WEB">网阅</el-radio>
          <el-radio :label="ICorrectType.HAND">手阅</el-radio>
          <el-radio :label="ICorrectType.PHOTO" v-if="isPhotoCorrect">拍改</el-radio>
        </el-radio-group>
        <div class="full-score" @click="isShowCorrectTpl = true"
          style="color: #409eff; cursor: pointer; font-size: 14px" v-if="Paper.correctType === ICorrectType.HAND">
          查看打分规范>
        </div>
      </div>
      <!-- 基本信息 -->
      <div class="silder-title-bar">
        基本信息
        <i class="dotbot"></i>
      </div>
      <div class="silder-content">
        <el-form ref="formRef" :model="Paper" label-width="85px" :disabled="Paper.isEdit">
          <el-form-item label="所属学科">
            <!-- 多学科只展示标签 -->
            <template v-if="Paper.multiSubjects.length">
              <el-tag class="subject-tag" type="info" v-for="sub in Paper.multiSubjects" :key="sub.subjectId">{{
                sub.subjectName }}</el-tag>
            </template>
            <el-select v-model="subjectId"
              :disabled="!(Paper.cardType == ICARDMODEL.BLANKCARD || Paper.cardType == ICARDMODEL.PHOTO)"
              @change="changeSubject" style="width: 63%" v-else>
              <el-option v-for="item in Paper.subjectList" :key="item.subjectId" :label="item.subjectName"
                :value="item.subjectId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="考号类型">
            <el-select v-model="Paper.numberLayout" @change="changeNumberLayout" style="width: 40%">
              <el-option label="条形码" :value="INUMBERLAYOUT.BARCODE" />
              <el-option label="准考证号" :value="INUMBERLAYOUT.TICKET" />
              <template v-if="!isPhotoCorrect">
                <el-option v-if="Paper.isHandWrite && Paper.stuNoLength <= 6" label="紧凑型"
                  :value="INUMBERLAYOUT.QRCODE" />
                <el-option v-if="Paper.isHandWrite" label="手写" :value="INUMBERLAYOUT.WRITE" />
                <el-option v-if="Paper.isMoreNoHeader" label="紧凑型（多位）" :value="INUMBERLAYOUT.MORE" />
              </template>
            </el-select>
            <!-- <el-button size="small" @click="customHeader">自定义</el-button> -->
            <!-- <el-radio-group v-model="Paper.numberLayout" @change="changeNumberLayout">
              <el-radio :label="INUMBERLAYOUT.BARCODE">条形码</el-radio>
              <el-radio :label="INUMBERLAYOUT.TICKET">准考证号</el-radio>
              <el-radio :label="INUMBERLAYOUT.WRITE">手写</el-radio>
            </el-radio-group> -->
            <el-select
              v-if="(Paper.numberLayout == INUMBERLAYOUT.TICKET || Paper.numberLayout == INUMBERLAYOUT.WRITE || Paper.numberLayout == INUMBERLAYOUT.MORE) && stuNoList.length > 1"
              v-model="Paper.stuNoLength" style="margin-left: 10px; width: 20%" @change="changeStuNo">
              <el-option v-for="item in stuNoList" :key="item" :label="item" :value="item">
              </el-option>
            </el-select>
            <el-checkbox style="margin-left: 5px;" v-if="Paper.numberLayout == INUMBERLAYOUT.TICKET && !isPhotoCorrect"
              v-model="Paper.isNumberHasX" label="使用X" />
          </el-form-item>
          <template v-if="!isPhotoCorrect">
            <el-form-item style="display: inline-flex;" label="启用密封线">
              <el-switch v-model="Paper.isSealLine" />
            </el-form-item>
            <el-form-item v-if="isShowAB" style="display: inline-flex;" label="AB卷">
              <el-switch v-model="Paper.isABPaper" />
            </el-form-item>
            <el-form-item label="制卡模式"
              v-if="Paper.cardType != ICARDMODEL.BLANKCARD && Paper.cardType != ICARDMODEL.ENGLISH">
              <el-radio-group :disabled="isLayoutChanging" v-model="cardType" @change="cardChange">
                <el-radio :label="ICARDMODEL.QUESCARD">题卡合一</el-radio>
                <el-radio :label="ICARDMODEL.ONLYCARD">仅答题卡</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="设置页眉">
              <el-button size="small" @click="customHeader">自定义</el-button>
            </el-form-item>
            <el-form-item class="layout-box" label="页面布局" v-if="Paper.cardType != ICARDMODEL.ENGLISH">
              <el-radio-group v-model="Paper.pageLayout" @change="layoutChange">
                <el-radio :label="IPAGELAYOUT.A4">
                  <div class="layout-tips">一栏</div>
                  <div class="layout-tips">A4/16K/B5</div>
                </el-radio>
                <el-radio :label="IPAGELAYOUT.A3">
                  <div class="layout-tips">两栏</div>
                  <div class="layout-tips">A3/8K/B4</div>
                </el-radio>
                <el-radio :label="IPAGELAYOUT.A33">
                  <div class="layout-tips">三栏</div>
                  <div class="layout-tips">A3/8K/B4</div>
                </el-radio>
                <el-radio :label="IPAGELAYOUT.A32">
                  <div class="layout-tips">正3反2</div>
                  <div class="layout-tips">A3/8K/B4</div>
                </el-radio>
                <el-radio :label="IPAGELAYOUT.A23">
                  <div class="layout-tips">正2反3</div>
                  <div class="layout-tips">A3/8K/B4</div>
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item>
              <template #label>
                全局<span v-click-five-times="(() => { isShowCancel = true })">修改</span>
              </template>
              <el-select v-model="Paper.fontSize" style="width: 35%; padding-right: 10px" @change="changeFont">
                <el-option v-for="item in fontSizeList" :key="item.value" :label="`字号:${item.text}`"
                  :value="item.value">
                </el-option>
              </el-select>
              <el-select v-model="Paper.space" style="width: 35%; padding-right: 10px" @change="changeFont">
                <el-option v-for="item in spaceList" :key="item.value" :label="`行距:${item.text}`" :value="item.value">
                </el-option>
              </el-select>
              <el-select v-model="Paper.fontFamily" style="width: 30%" @change="changeFont">
                <el-option v-for="item in fontFamiltList" :key="item.value" :label="item.text" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="Paper.isHandWriteAns && !Paper.isEnglishSubject() && cardType == ICARDMODEL.QUESCARD && !Paper.ansToTopConfig.object"
              label="手写作答">
              <template #label>
                <el-tooltip content="开启后，选择题直接在括号内作答，系统智能批改" placement="right">
                  <span>手写作答<el-icon>
                      <QuestionFilled />
                    </el-icon></span>
                </el-tooltip>
              </template>
              <el-radio-group v-model="Paper.ansType" @change="changeAnswerType">
                <el-radio :label="ANSWER_TYPE.write">是</el-radio>
                <el-radio :label="ANSWER_TYPE.fill">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="cardType == ICARDMODEL.QUESCARD" label="作答前置">
              <el-checkbox v-if="Paper.ansType != ANSWER_TYPE.write" v-model="Paper.ansToTopConfig.object"
                @change="answerPrefix">客观题</el-checkbox>
              <el-checkbox v-model="Paper.ansToTopConfig.fill" @change="answerPrefix">填空题</el-checkbox>
            </el-form-item>
            <el-form-item v-if="cardType == ICARDMODEL.QUESCARD" label="图文排版">
              <el-radio-group v-model="Paper.pageType" :disabled="Paper.isEdit">
                <el-radio :label="0">常规</el-radio>
                <el-radio :label="1">紧凑</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="客观题合并" v-if="cardType === ICARDMODEL.ONLYCARD || isBlankCard">
              <el-switch v-model="Paper.mergeQues" :inactive-value="0" :active-value="1" @change="switchMergeQues" />
            </el-form-item>
          </template>
        </el-form>
      </div>

      <div class="sticky-title">
      <!-- 添加试题 -->
      <template v-if="showQuesAddBtn">
        <div class="silder-title-bar">
          <i class="dotbot"></i>
          添加试题
        </div>

        <el-row :gutter="20" class="add-ques-list" :class="{ 'pointer-none': Paper.isEdit }">
          <el-col :span="6" v-for="item in addQuesList" :key="item.name">
            <el-button size="small" class="add-ques click-element" @click="addNewQues(item)" :disabled="Paper.isEdit"
              v-if="!isPhotoCorrect || (isPhotoCorrect && item.cate === 'objective')">
              <span style="font-size: 20px; margin-top: -2px; margin-right: 3px">+</span>
              <span>{{ item.name }}</span>
            </el-button>
          </el-col>
        </el-row>

      </template>

      <!-- 试卷结构 -->
      <div class="silder-title-bar clearfix">
        <div class="pull-left click-element" @click="toggleAllListCollapsed">
          <i class="dotbot"></i>
          试卷结构
          <el-icon v-if="showNoAnswerAddBtn" title="添加非作答区" @click="()=>{addNewQues({type:QUES_TYPE.noAnswerArea})}"><CirclePlus /></el-icon>
        </div>

        <div class="full-score" v-if="quesInfo.length">
          试卷总分：{{ fullScore }}
        </div>
      </div>
      </div>
      <div style="display: none;">{{ refreshKey }}</div>
      <div v-if="quesInfo.length" class="ques-list">
        <draggable v-model="quesInfo" :disabled="Paper.abType" delay="100" animation="500" itemKey="id"
          handle=".click-element" fallbackTolerance="20" :force-fallback="true" group="list" :fallback-class="true"
          :fallback-on-body="true" @end="onDragEnd">
          <template #item="{ element, index }">
            <div :name="index" :key="element.id" class="big-ques" v-show="!element.parentId">
              <!-- 用于触发组件其他深层结构刷新 -->
              <div style="display: none;">{{ refreshKey }}</div>
              <!-- 非作答区noAnswerArea仅显示删除 -->
              <template v-if="element.typeId == QUES_TYPE.noAnswerArea">
                <el-row class="big-ques-row no-bg click-element">
                  <el-col :span="6" class="ques-title" style="text-align: left">
                    {{ element.name }}
                  </el-col>
                  <el-col :span="14"></el-col>
                  <el-col :span="4">
                    <i class="icon edit" style="visibility: hidden;"></i>
                    <i class="icon remove" @click="deleteQuesConfirm(element)"></i>
                  </el-col>
                </el-row>
              </template>

              <template v-else>
                <!-- ####折叠面板标题区#### -->
                <!-- 包含parentId为混合题子集，这里不单独为小题设置修改 -->
                <el-row class="big-ques-row" :class="{ collapsed: element.collapsed }" v-if="!element.parentId">
                  <div class="ques-title--cover" title="点击切换展示小题列表" @click="element.collapsed = !element.collapsed">
                  </div>
                  <el-col :span="6" class="ques-title">
                    <el-input class="pointer-auto" v-model="element.name" @change="changeBigQuesName(element)"
                      :disabled="Paper.isEdit"></el-input>
                  </el-col>

                  <el-col :span="14" class="ques-score">
                    共{{ element.count }}题,
                    <template v-if="element.typeId !== QUES_TYPE.mixin">
                      每题<el-input class="pointer-auto" v-model="element.quesScore"
                        @change="changeBigQuesPerScore(element, index)" @input="handleInput($event, index)"
                        :disabled="Paper.isEdit">
                      </el-input>分,
                    </template>
                    共{{ element.score }}分
                  </el-col>
                  <el-col class="ques-handle click-element" :span="4" v-if="!Paper.isEdit">
                    <i v-if="Paper.cardType != ICARDMODEL.ENGLISH" class="icon edit pointer-auto"
                      @click="handleEditView(element, index)"></i>
                    <i class="icon remove pointer-auto" @click="deleteQuesConfirm(element)"></i>
                  </el-col>
                </el-row>

                <!-- ####折叠面板内容区#### -->
                <el-collapse-transition v-if="element.data.length > 0">
                  <div class="big-ques-list" v-show="!element.collapsed">
                    <!-- 混合题子集题目 -->
                    <template v-if="element.typeId == QUES_TYPE.mixin">
                      <!-- childIndex：所在混合题的子集索引 -->
                      <el-row>
                        <!-- <el-col :span="2"></el-col> -->
                        <el-col :span="5">小题</el-col>
                        <el-col :span="5">题型</el-col>
                        <el-col :span="5">分数</el-col>
                      </el-row>

                      <el-row class="small-ques-row" v-for="(subitem, subindex) in element.data" :key="subitem.id">
                        <!-- <el-col :span="2"></el-col> -->

                        <el-col :span="5">
                          <el-input v-model="subitem.quesName" :disabled="Paper.isEdit"
                            @change="changeSmallMixQuesName(subitem)"></el-input>
                        </el-col>

                        <el-col :span="5">
                          {{ subitem.name }}
                        </el-col>

                        <el-col :span="5">
                          {{ subitem.score }}
                        </el-col>
                        <el-col :span="2" v-if="!Paper.isEdit">
                          <i class="icon edit edit-small pointer-auto click-element"
                            @click="handleEditSmallView(subitem, index, subindex)"></i>
                        </el-col>
                      </el-row>
                    </template>

                    <!-- 非混合题 -->
                    <template v-else>
                      <el-row>
                        <!-- <el-col :span="0.5"></el-col> -->
                        <el-col :span="getColWidth(element.typeId)">小题</el-col>
                        <el-col :span="getColWidth(element.typeId)"
                          v-if="Paper.isObjectiveQues(element.typeId) || Paper.checkQuesIsMixin(element)">答案</el-col>
                        <el-col :span="getColWidth(element.typeId)">分数</el-col>
                        <el-col :span="getColWidth(element.typeId)"
                          v-if="element.typeId == QUES_TYPE.choice">半对给分</el-col>
                        <el-col :span="4"></el-col>
                      </el-row>

                      <el-row class="small-ques-row" v-for="(subitem, subindex) in element.data" :key="subitem.id"
                        v-show="!subitem.parentId">
                        <!-- <el-col :span="0.5"></el-col> -->

                        <el-col :span="getColWidth(element.typeId)">
                          <el-input v-model="subitem.quesNos"
                            :disabled="Paper.isEdit || subitem.isMergeImg || subitem.isChooseDo"
                            @change="changeSmallQuesName(subitem)"></el-input>
                        </el-col>

                        <el-col :span="getColWidth(element.typeId)" v-if="Paper.isObjectiveQues(subitem.typeId)">
                          <el-select class="answer-select"
                            :placeholder="TFNameList[subitem.judgeType || element.judgeType].join('/')"
                            v-model="subitem.answer" @change="handleTFChange(subitem)"
                            v-if="subitem.typeId == QUES_TYPE.judge">
                            <el-option v-for="(subItemTF, subIndex) in TFNameList[
                              subitem.judgeType || element.judgeType
                            ]" :key="(subitem.judgeType || element.judgeType) + subItemTF" :label="subItemTF"
                              :value="TFAnswers[subIndex]" />
                          </el-select>

                          <el-input :disabled="Paper.isEdit" v-model="subitem.answer"
                            @input="handleChoiceChange(subitem)" v-else></el-input>
                        </el-col>

                        <el-col :span="getColWidth(element.typeId)"
                          v-else-if="Paper.checkQuesIsMixin(element)">--</el-col>

                        <el-col :span="getColWidth(element.typeId)">
                          <el-input v-model="subitem.score" @change="
                            changeSmallQuesSingleScore(element, subitem, index)
                            " @input="checkScoreRules(subitem)"
                            :disabled="Paper.isEdit || subitem.isChooseDo"></el-input>
                        </el-col>
                        <!-- 多选题半对给分 -->
                        <el-col :span="getColWidth(element.typeId)" v-if="subitem.typeId == QUES_TYPE.choice">
                          <el-input v-if="subitem.ruleType == IRULERTYPE.STANDAD" v-model="subitem.halfScore"
                            @input="checkScoreRules(subitem)"
                            @change="changeSmallQuesHalfScore(element, subitem, index)"
                            :disabled="Paper.isEdit"></el-input>
                          <span v-else-if="subitem.ruleType == IRULERTYPE.EXAM">新高考</span>
                          <span v-else-if="subitem.ruleType == IRULERTYPE.BREAK">断句题</span>
                          <span v-else>自定义</span>
                        </el-col>
                        <el-col :span="4" v-if="!Paper.isEdit && !subitem.isChooseDo">
                          <i v-if="Paper.cardType != ICARDMODEL.ENGLISH"
                            class="icon edit edit-small pointer-auto click-element"
                            @click="handleEditSmallView(subitem, index, subindex)"></i>
                          <i v-if="subitem.level != 3 && !subitem.isMergeImg"
                            class="icon remove pointer-auto click-element"
                            @click="deleteSmallQuesConfirm(element, subitem)"></i>
                        </el-col>

                      </el-row>
                    </template>
                  </div>
                </el-collapse-transition>
              </template>
            </div>
          </template>
        </draggable>

        <br />
        <br />
      </div>
      <el-empty v-else description="请添加题目" />
    </div>

    <el-row class="footer-btn">
      <template v-if="Paper.editerType != IEditerPowerType.TPL_NO_POWER">
        <el-button size="large" v-if="Paper.isEdit && !Paper.isRelatedWork && isShowCancel" type="danger"
          @click="cancelOver">
          取消完成
        </el-button>
        <template v-if="!Paper.isEdit">
          <el-button size="large" type="default" title="可使用 Ctrl+S 快捷保存" @click="saveEvent(0)" :loading="loading">
            保存
          </el-button>
          <el-button size="large" v-if="isShowReset" type="danger" @click="resetPaper">
            重置
          </el-button>
          <el-button size="large" type="default" @click="preview">
            预览
          </el-button>
          <el-button size="large" type="default" @click="openOverCardToast(1)"
            v-if="!isPhotoCorrect && Paper.isTemplate === 0">
            完成制卡
          </el-button>
          <el-button size="large" type="primary" @click="openOverCardToast(2)" :loading="loading"
            v-if="(isHandCorrect || isPhotoCorrect) && Paper.isTemplate === 0 && !Paper.abType">
            布置
          </el-button>
        </template>
      </template>
      <template v-else>
        <el-button size="large" type="default" @click="cloneCard">
          复用
        </el-button>
      </template>
    </el-row>

    <!-- 基本信息弹窗 -->
    <send-view v-if="sendParam.isShow" :visible="sendParam.isShow" :info="sendParam.info"
      @close-dialog="closeSendDialog"></send-view>

    <!-- 添加/编辑单大题弹窗 -->
    <edit-single-ques :quesType="singleQuesType" :isShowDialog="showSingleDialog" :index="singleDataIndex"
      :isWriting="singleIsWriting" :mode="singleDialogMode" @close-dialog="closeDialog" @update-question="updateAddQues"
      @update-mixin-question="handleUpdateMixinQuestions" v-if="showSingleDialog">
    </edit-single-ques>

    <!-- 添加/编辑混合题弹窗 -->
    <edit-mixin-ques :isShowDialog="showMixinDialog" :index="mixinDataIndex" :mode="mixinDialogMode"
      @close-dialog="closeDialog" @update-question="handleUpdateMixinQuestions" v-if="showMixinDialog">
    </edit-mixin-ques>

    <!-- 编辑小题弹窗 -->
    <edit-small-ques :isShowDialog="showSmallDialog" :item="smallQues" :singleDataIndex="singleDataIndex"
      :smallDataIndex="smallDataIndex" @close-dialog="closeDialog" @update-small-question="handleUpdateSmallQuestions"
      v-if="showSmallDialog"></edit-small-ques>

    <el-dialog v-if="isShowCorrectTpl" v-model="isShowCorrectTpl" width="740px" title="老师批改打分规范">
      <el-image style="width: 740px;" fit="contain" :src="require('@/assets/correct-tpl.png')" />
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="isShowCorrectTpl = false">我知道了</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  computed,
  getCurrentInstance,
  ComponentInternalInstance,
  watch,
  nextTick,
  ref,
} from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
import {
  IBaseModel,
  IBigQues,
  ICARDMODEL,
  ICorrectType,
  IPAGELAYOUT,
  QUES_TYPE,
  QUES_SCAN_MODE,
  ANSWER_TYPE,
  INUMBERLAYOUT,
  JUDGE_TYPE,
  ISmallQues,
  IRULERTYPE,
  IEditerPowerType,
  MARK_TYPE,
  ICARD_STATE,
  IAB_CARD_SHEEFT_TYPE,
} from '@/typings/card';
import { arabToChinese, base64toFile, generateUUID, getQueryString } from '../../utils/util';
import { getHtmlText } from '@/utils/dom';
import sendView from './sendView.vue';
import Paper from '../paper';
import {
  updateViewPaper,
  examCard,
  overCard,
  relateAnswerCard,
  clearTestBankQueInfo,
  copyCardPaper,
  updateNotOverCard,
  clearPDFCache,
} from '@/service/api';
import bus from '@/utils/bus';
import { ElLoading, ElMessageBox } from 'element-plus';
import editMixinQues from './editMixinQues.vue';
import editSingleQues from './editSingleQues.vue';
import editSmallQues from './editSmallQues.vue';
import { PaperConstant } from '../paper.constant';
import Draggable from 'vuedraggable';

export default defineComponent({
  components: {
    sendView,
    editMixinQues,
    editSingleQues,
    editSmallQues,
    Draggable,
  },
  emits: ['preview', 'save'],
  setup(props: any, ctx) {
    const route = useRoute();
    const state = reactive({
      //批阅方式
      ICorrectType: ICorrectType,
      //页面布局
      IPAGELAYOUT: IPAGELAYOUT,
      //制卡模式
      ICARDMODEL: ICARDMODEL,
      // 考号模式
      INUMBERLAYOUT: INUMBERLAYOUT,
      IEditerPowerType: IEditerPowerType,
      //题目类型
      QUES_TYPE: QUES_TYPE,
      ANSWER_TYPE: ANSWER_TYPE,
      Paper: Paper,
      cardType: Paper.cardType,
      quesInfo: Paper.quesInfos,
      //布局
      layout: IPAGELAYOUT.A4,
      IRULERTYPE: IRULERTYPE,
      subjectId: Paper.subjectId,
      stuNoList: Paper.stuNoList,
      /* 判断正误的属性列表 */
      TFNameList: PaperConstant.TFNameList,
      TFAnswers: PaperConstant.TFAnswers,
      //学科列表
      subjectList: [
        {
          subjectId: '1',
          subjectName: '数学',
        },
      ],
      //字号集合
      fontSizeList: PaperConstant.FontSizeList,
      //行距集合
      spaceList: PaperConstant.SpaceList,
      //字体集合
      fontFamiltList: PaperConstant.FontFamiltList,
      //满分
      fullScore: 0,
      //题目集合
      quesList: [],
      //提交按钮加载中状态
      loading: false,
      //布置作业弹框
      sendParam: {
        isShow: false,
        info: {},
      },
      isShowCorrectTpl: false,
      addQuesList: PaperConstant.QuesTypeList,
      isProd: process.env.VUE_APP_ENV == 'prod',

      // 显示混合题弹窗
      showMixinDialog: false,
      // 混合题弹窗模式:add|edit
      mixinDialogMode: 'add',
      // 混合题数据索引
      mixinDataIndex: 0,

      // 显示单大题弹窗
      showSingleDialog: false,
      showSmallDialog: false,
      // 单大题弹窗模式:add|edit
      singleDialogMode: 'add',
      // 单大题当前对应的类型
      singleQuesType: '',
      // 混合题数据索引
      singleDataIndex: 0,
      singleIsWriting: 0,
      // 单小题题当前对应的类型
      smallQues: null,
      smallDataIndex: 0,
      // 是否合并客观题
      isMergeQues: false,
      // 是否英语学科
      isEnglishSubject: Paper.isEnglishSubject(),
      isBlankCard: Paper.cardType == ICARDMODEL.BLANKCARD,
      showQuesAddBtn: Paper.cardType == ICARDMODEL.BLANKCARD || Paper.cardType == ICARDMODEL.PHOTO,
      showNoAnswerAddBtn: Paper.cardType == ICARDMODEL.QUESCARD || Paper.cardType == ICARDMODEL.ONLYCARD,
      // 正在切换页面布局
      isLayoutChanging: false,

      // 强制刷新key
      refreshKey: 0,
      isShowReset: false,
      isShowCancel: false,
    });

    let mergeQuesBefore = Paper.mergeQues;

    // 动态获取题目结构显示列宽
    const getColWidth = typeid => {
      return typeid == QUES_TYPE.choice ? 5 : 5;
    };

    const isShowAB = computed(() => {
      return state.isBlankCard && !state.Paper.abType && Number(route.query.relateCardType) != ICARD_STATE.abPaperTwo;
    });

    // 是否手阅类型
    const isHandCorrect = computed(() => {
      return state.Paper.correctType == ICorrectType.HAND;
    });

    // 是否拍改类型
    const isPhotoCorrect = computed(() => {
      return state.Paper.correctType == ICorrectType.PHOTO;
    })

    /* 快捷键 */
    document.addEventListener('keydown', function (event) {
      if (event.ctrlKey) {
        if (event.key.toLowerCase() === 's') {
          event.preventDefault();
          saveEvent(0);
        } else if (event.key.toLowerCase() === 'p') {
          event.preventDefault();
          preview();
        }
      }
    });

    Paper.on('updateQustions', from => {
      // if (from !== 'card') return;

      // 监听到来自卡片的更新
      state.quesInfo = Paper.getQuesInfos();
      state.refreshKey++;
    });

    /* 同步答案到卡片 */
    const syncAnswerToCard = (ques: any, mode: "" | "AB" = "") => {
      if (mode != "AB") {
        const data = {
          from: Paper.abType,
          type: 'updateQuestions',
          func: 'changeAnswer',
          arguments: [ques],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
      // 只有仅答题卡的情况需要单独同步
      if (state.Paper.cardType != ICARDMODEL.ONLYCARD) return;

      let quesInfosInCard = Paper.quesInfosInCard;
      quesInfosInCard.forEach(qItem => {
        if (qItem.typeId !== ques.typeId) return;

        qItem.data.forEach(subQItem => {
          if (subQItem.id !== ques.id) return;

          subQItem.answer = ques.answer;
        });
      });
      Paper.notifyUpdateAllData();
    };

    /* 处理判断题改变答案 */
    const handleTFChange = (item: any) => {
      syncAnswerToCard(item);
    };

    /* 处理选择改变答案 */
    const handleChoiceChange = (item: any) => {
      if (item.typeId == QUES_TYPE.choice) {
        item.answer = item.answer.replace(/[A-Z]+[,]?/g, function (match: any) {
          return match.join('').replace(/,/g, '').split('').join(',');
        });
      } else if (item.typeId == QUES_TYPE.singleChoice) {
        item.answer = item.answer.replace(/[^A-Z]/g, '')[0] || '';
      }

      // TIP: 这里需要手动同步客观题答案，兼容题卡合一混合题导致数据隔离无法同步的情况
      syncAnswerToCard(item);
    };

    const checkScoreRules = (value: any) => {
      let score = value.score.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '');
      let parts = score.split('.');
      if (parts.length > 1) {
        parts = parts.splice(0, 2);
        // 如果有小数点
        parts[1] = parts[1].slice(0, 1); // 仅保留一位小数
      }
      score = parts.join('.')
      if (score > 100) {
        score = 100
      }
      value.score = score;
    };

    // 监听Paper数据变化时统计总分值
    watch(
      () => state.quesInfo,
      (value, oldValue) => {
        state.fullScore = Paper.getTotalScore();
      },
      {
        deep: true,
        immediate: true,
      }
    );

    /* 切换学科 */
    const changeSubject = (val: any, mode: '' | 'AB' = '') => {
      state.subjectId = val;
      Paper.subjectId = val;
      Paper.setHasLineBySubject(Paper.subjectId);
      Paper.setIsCanFillEvaBySubject(Paper.subjectId);
      if (!Paper.isCanFillEva) {
        Paper.cancelAIScan();
        state.quesInfo = Paper.getQuesInfos();
        Paper.updateAndMergeQuestions(state.quesInfo);
        bus.emit('updateQuesInfos');
      }
      if (mode != 'AB') {
        const data = {
          from: Paper.abType,
          type: 'updateInfos',
          func: 'changeSubject',
          arguments: [val],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
    };

    /**
     * @name 改变字体触发事件
     * @param item
     */
    const changeFont = (item: IBaseModel) => {
      nextTick(() => {
        bus.emit("renderOpts");
        bus.emit("renderAllCard");
      });
    };

    const changeStuNo = (stuNoLength: number) => {
      state.Paper.stuNoLength = stuNoLength;
    };

    const convertSort = (sort: number) => {
      return arabToChinese(sort);
    };

    /**
     * @name 修改答题标题
     * @param item
     */
    const changeQuesName = () => {
      Paper.updateAndMergeQuestions(state.quesInfo);
      Paper.notifyUpdateData('right');

    };

    /**
     * @name 修改大题标题
     * @param item
     */
    const changeBigQuesName = (item: IBigQues, mode: '' | 'AB' = '') => {
      if (item.fullname) item.fullname = item.name;
      changeQuesName();
      if (mode != 'AB') {
        const data = {
          from: Paper.abType,
          type: 'updateQuestions',
          func: 'changeBigQuesName',
          arguments: [item],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
    };

    /**
     * @name 修改小题小问标题
     * @param item
     */
    const changeSmallQuesName = (item: any, mode: '' | 'AB' = '') => {
      if (mode != 'AB') {
        const data = {
          from: Paper.abType,
          type: 'updateQuestions',
          func: 'changeSmallQuesName',
          arguments: [item],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
    }

    /**
     * @name 修改混合题小题小问标题
     * @param item
     */
    const changeSmallMixQuesName = (subitem: any, mode: '' | 'AB' = '') => {
      let qItem: any = state.quesInfo.find(item => item.id === subitem.id);
      qItem.quesName = subitem.quesName;
      qItem.quesNos = subitem.quesName;
      // 混合题修改小问quesNos
      if (qItem.data && qItem.data.length === 1) {
        qItem.data[0].quesName = subitem.quesName;
        qItem.data[0].quesNos = subitem.quesName;
      }
      subitem.quesNos = subitem.quesName;
      if (qItem.quesFullName) qItem.quesFullName = qItem.quesName;
      changeQuesName();
      if (mode != 'AB') {
        const data = {
          from: Paper.abType,
          type: 'updateQuestions',
          func: 'changeSmallMixQuesName',
          arguments: [subitem],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
    };

    /**
     * @name 修改大题的平均分
     * @param item 大题数据
     * @param index 大题下标
     */
    const changeBigQuesPerScore = (item: IBigQues, index: number, mode: '' | 'AB' = '') => {
      let totalScore = 0;
      let preScore = Number(item.quesScore);

      // 混合模式大题，将平均设置到每小题，子题目被动平均
      let childLenMap: Map<string, number> = new Map();
      item.data.forEach((subItem: any) => {
        if (item.mixinMode) {
          // 父级和非子集题目直接设置平均分值
          if (subItem.mixinMode || !subItem.parentId) {
            subItem.score = preScore;
            totalScore += preScore;
          }
          // 子集再进行分值平摊
          if (subItem.mixinMode) {
            let parentId = subItem.id;
            let childrenLength = item.data.filter(
              (ssubItem: any) => ssubItem.parentId === parentId
            ).length;

            childLenMap.set(parentId, childrenLength);
          }
          if (subItem.parentId && childLenMap.size > 0) {
            subItem.score = preScore / childLenMap.get(subItem.parentId);
          }
        } else {
          // 普通答题，直接设置每小题分数
          subItem.score = preScore;
          if (subItem.isChooseDo && !subItem?.targetIds?.includes(subItem.id)) return;
          totalScore += (preScore * 10);
        }
      });
      if (item.doQuesList && item.doQuesList.length) {
        item.doQuesList.forEach(doItem => {
          doItem.score = preScore
        })
      }
      totalScore = totalScore / 10;
      item.score = totalScore;
      // 将分数同步到自定义标题
      if (item.fullname && mode != 'AB') {
        item.fullname = Paper.convertQuesName(item.fullname) + ` (共${totalScore}分)`;
      }

      let queslist = Paper.updateAndMergeQuestions(index, item);
      Paper.notifyUpdateData('right');
      state.fullScore = Paper.getTotalScore();
      setTimeout(() => {
        bus.emit('updateQuesInfos', index);
      }, 100);
      if (mode != 'AB') {
        const data = {
          from: Paper.abType,
          type: 'updateQuestions',
          func: 'changeBigQuesPerScore',
          arguments: [item, index],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
    };

    /**
     * @name 修改小题的单独分值
     * @param item 大题数据
     * @param subItem 小题数据
     * @param index 大题下标
     */
    const changeSmallQuesSingleScore = (item: IBigQues, subItem: any, index: number, mode: '' | 'AB' = '') => {
      let totalScore = 0;
      let curScore = Number(subItem.score);
      if (subItem.mixinMode) {
        // 混合模式大题,小题设置平均分
        let parentId = subItem.id;
        let childrenLength = item.data.filter(
          (ssubItem: any) => ssubItem.parentId === parentId
        ).length;
        let preScore = curScore / childrenLength;
        item.data.forEach((ssubItem: any) => {
          if (parentId !== ssubItem.parentId) return;

          ssubItem.score = preScore;
        });
      }
      let isAverage = true;
      item.data.forEach((ssubItem: any) => {
        if (ssubItem.parentId) return;
        if (ssubItem.isChooseDo && !ssubItem?.targetIds?.includes(ssubItem.id)) return;
        totalScore += Number(ssubItem.score * 10);
        if (ssubItem.score != curScore) {
          isAverage = false;
        }
      });

      item.score = totalScore / 10;
      if (isAverage) {
        item.quesScore = curScore;
      } else {
        item.quesScore = '';
      }
      Paper.updateAndMergeQuestions(index, item);
      Paper.notifyUpdateData('right');
      state.fullScore = Paper.getTotalScore();
      setTimeout(() => {
        bus.emit('updateQuesInfos', index);
      }, 100);
      if (mode != 'AB') {
        const data = {
          from: Paper.abType,
          type: 'updateQuestions',
          func: 'changeSmallQuesSingleScore',
          arguments: [item, subItem, index],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
    };

    /**
     * @name 修改小题的半对分值
     * @param item 大题数据
     * @param subItem 小题数据
     * @param index 大题下标
     */
    const changeSmallQuesHalfScore = (item: IBigQues, subItem: any, index: number, mode: '' | 'AB' = '') => {
      if (mode != 'AB') {
        const data = {
          from: Paper.abType,
          type: 'updateQuestions',
          func: 'changeSmallQuesHalfScore',
          arguments: [item, subItem, index],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
    };

    /**
     * @name 是否显示表格选项
     * @param typeId
     */
    const isShowTableOption = (item: any) => {
      let iSShow = false;
      item.data.forEach((sitem: any) => {
        if (Paper.isChoiceQues(sitem.typeId)) {
          iSShow = true;
        }
      });
      return iSShow;
    };

    /**
     * @name: 修改单大题题弹框
     * @param index 大题下标
     */
    const openSingleEditView = (index: number) => {
      state.singleDialogMode = 'edit';
      state.singleDataIndex = index;
      let qType: any = Paper.getQuesInfos()[index].typeId;
      state.singleQuesType = qType;
      state.showSingleDialog = true;
    };

    /**
     * @name: 修改混合题弹框
     * @param index 大题下标
     */
    const openMixinEditView = (index: number) => {
      state.mixinDialogMode = 'edit';
      state.mixinDataIndex = index;
      state.showMixinDialog = true;
    };

    const deleteQuesConfirm = async (item: IBigQues) => {
      await ElMessageBox.confirm('题目删除后无法恢复，是否确认？', '提示', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      });

      deleteQues(item);
    };

    const deleteSmallQuesConfirm = async (bitem: IBigQues, sitem: ISmallQues) => {
      await ElMessageBox.confirm('题目删除后无法恢复，是否确认？', '提示', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      });
      deleteSmallQues(bitem, sitem);
    };

    /**
     * @name:删除大题
     */
    const deleteQues = (item: IBigQues, mode: '' | 'AB' = '') => {
      let id = item.id;
      if (item.typeId == QUES_TYPE.mixin) {
        // 删除混合大题需要删除所有相关自己题目
        state.quesInfo = state.quesInfo.filter(item => {
          return item.id != id || (item.parentId && item.parentId != id);
        });
        state.quesInfo = state.quesInfo.filter(item => item.parentId != id);
      } else {
        state.quesInfo = state.quesInfo.filter(item => item.id != id);
      }

      Paper.updateAndMergeQuestions(state.quesInfo);
      Paper.notifyUpdateData('right');
      bus.emit('orderQuesInfos');
      if (mode != 'AB') {
        const data = {
          from: Paper.abType,
          type: 'updateQuestions',
          func: 'deleteQues',
          arguments: [item],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
    };

    /**
     * @name:删除小题
     */
    const deleteSmallQues = (bitem: IBigQues, sitem: ISmallQues, mode: '' | 'AB' = '') => {
      let bigQ = state.quesInfo.find(item => {
        return item.id == bitem.id;
      });
      if (bigQ.data.length == 1) {
        state.quesInfo = state.quesInfo.filter(item => item.id != bitem.id);
      } else {
        bigQ.data = bigQ.data.filter(item => item.id != sitem.id && item.parentId != sitem.id);
        let count = 0;
        let score = 0;
        bigQ.data.forEach(qs => {
          if (!qs.parentId) {
            score += Number(qs.score);
            count++;
          }
        });
        bigQ.count = count;
        bigQ.score = score;
      }

      Paper.updateAndMergeQuestions(state.quesInfo);
      Paper.notifyUpdateAllData();
      bus.emit('deleteQuesInfos');
      if (mode != 'AB') {
        const data = {
          from: Paper.abType,
          type: 'updateQuestions',
          func: 'deleteSmallQues',
          arguments: [bitem, sitem],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
    };

    /**
     * @name: 监听分值输入框输入事件
     * @param value
     */
    const handleInput = (value: any, index: number) => {
      value = value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '');
      let parts = value.split('.');
      if (parts.length > 1) {
        parts = parts.splice(0, 2);
        // 如果有小数点
        parts[1] = parts[1].slice(0, 1); // 仅保留一位小数
      }
      value = parts.join('.');
      if (value > 100) {
        value = 100
      }
      Paper.updateAndMergeQuestions(index, 'quesScore', value);
    };

    /**
     * @name 页面一开始加载
     */
    const handleEditView = (item: any, index: number) => {
      state.singleIsWriting = item.isWriting;
      state.singleQuesType = item.type;

      let isMixin = item.typeId == QUES_TYPE.mixin;
      // 处理题库加工过的混合题
      if (!state.isBlankCard) {
        // 非空白题卡的简答题需要检测是否混合题
        isMixin = Paper.checkQuesIsMixin(item);
      }

      if (isMixin) {
        openMixinEditView(index);
      } else {
        openSingleEditView(index);
      }
    };

    const handleEditSmallView = (item: any, index: number, subindex: number) => {
      state.smallQues = item;
      state.singleDataIndex = index;
      state.smallDataIndex = subindex;
      state.showSmallDialog = true;
    };

    /**
     * @name: 关闭编辑弹框
     */
    const closeDialog = async () => {
      state.showMixinDialog = false;
      state.showSingleDialog = false;
      state.showSmallDialog = false;
      state.fullScore = Paper.getTotalScore();
    };

    /**
     * @name: 更新题目数据
     * @param quesData 题目数据
     */
    const updateAddQues = async (quesData: IBigQues, index: number, mode: 'edit' | 'add' | 'AB') => {
      quesData.key = new Date().getTime();
      Paper.caculBigQuesScore(quesData);
      Paper.updateQuestions(index, quesData);
      state.quesInfo = Paper.getQuesInfos();
      //修改大题属性同步至小题属性
      let item = state.quesInfo[index];
      item.data.forEach(sitem => {
        sitem.decimal = item.decimal;
        sitem.gridType = item.gridType;
        sitem.gridPlace = item.gridPlace;
        sitem.halfScore = item.halfScore || sitem.halfScore;
        sitem.markType = item.markType || sitem.markType || Paper.markType;
        sitem.step = item.step || sitem.step || Paper.step;
        if (Paper.isFillQues(item.typeId) || Paper.isFillQues(sitem.typeId)) {
          sitem.judgeType = JUDGE_TYPE.MARK;
          if(item.scanMode == QUES_SCAN_MODE.AI_FILL){
            item.fillDistance = Math.min(Math.max(item.fillDistance,10),15);
          }
        } else {
          sitem.judgeType = item.judgeType || sitem.judgeType || Paper.judgeType;
        }
      });
      Paper.updateAndMergeQuestions(index, quesData);
      state.fullScore = Paper.getTotalScore();

      Paper.notifyUpdateData('right');
      Paper.notifyUpdateAllData();
      bus.emit('updateQuesInfos');
      if (mode != 'AB') {
        const data = {
          from: Paper.abType,
          type: 'updateQuestions',
          func: 'updateAddQues',
          arguments: [quesData, index],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
      // console.log("quesData:", quesData);
    };

    /* 处理混合题数据更新 */
    const handleUpdateMixinQuestions = async (quesData: IBigQues, index: number, mode: 'edit' | 'add' | 'AB') => {
      Paper.caculBigQuesScore(quesData);
      Paper.updateQuestions(index, quesData);
      state.quesInfo = Paper.getQuesInfos();
      // 更新题目数据并触发合并
      let queslist = Paper.updateAndMergeQuestions(Paper.quesInfos);
      await nextTick();

      // TIP 混合题需要通知所有组件
      Paper.notifyUpdateData('right');
      Paper.notifyUpdateAllData();
      bus.emit('updateQuesInfos', queslist);

      //关闭编辑弹框
      closeDialog();
      if (mode != 'AB') {
        const data = {
          from: Paper.abType,
          type: 'updateQuestions',
          func: 'handleUpdateMixinQuestions',
          arguments: [quesData, index],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
    };

    const handleUpdateSmallQuestions = (item: any, singleDataIndex: number, smallDataIndex: number, mode: '' | 'AB') => {
      let bigQues = state.quesInfo[singleDataIndex];
      bigQues.data[smallDataIndex] = item;
      bigQues.key = new Date().getTime();
      Paper.caculBigQuesScore(bigQues);
      state.quesInfo = Paper.getQuesInfos();
      Paper.updateAndMergeQuestions(singleDataIndex, state.quesInfo[singleDataIndex]);

      if (bigQues.typeId == QUES_TYPE.mixin) {
        // 混合题从大题队列中找到题目
        let qIndex = Paper.findBigIndexByQuesId(item.id);
        Paper.updateAndMergeQuestions(qIndex, item);
      }

      bus.emit('updateQuesInfos');
      Paper.notifyUpdateAllData();
      state.fullScore = Paper.getTotalScore();
      state.refreshKey++;
      //关闭编辑弹框
      closeDialog();
      if (mode != 'AB') {
        const data = {
          from: Paper.abType,
          type: 'updateQuestions',
          func: 'handleUpdateSmallQuestions',
          arguments: [item, singleDataIndex, smallDataIndex],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
    };

    /**
     * @name: 题卡模式切换
     */
    const cardChange = async (value: number) => {
      if (value == ICARDMODEL.QUESCARD) {
        Paper.onlyCard2quesCard();
        Paper.splitObjectiveQuestions();
      } else {
        Paper.quesCard2onlyCard(Paper.getQuesInfos(), true);
        Paper.mergeQues == 1 && Paper.mergeObjectiveQuestions();
      }
      Paper.ansType = ANSWER_TYPE.fill;
      Paper.cardType = value;
      bus.emit('switchCard');
    };

    /**
     * @name: 页面布局切换
     */
    const layoutChange = (value: number) => {
      state.isLayoutChanging = true;
      changeStuNo(state.Paper.stuNoLength);
      if (Paper.cardType == ICARDMODEL.QUESCARD) {
        bus.emit('switchLayout');
      } else {
        Paper.changePageLayout(value);
        bus.emit('reRenderMathJax');
      }
    };

    /**
     * @description: 作答前置
     * @return {*}
     */
    const answerPrefix = async () => {
      bus.emit('switchCorrect', { correctType: Paper.correctType });
      await nextTick();
      setTimeout(async () => {
        await nextTick();
        bus.emit('renderAllCard');
      }, 100);
    };

    const changeAnswerType = () => {
      bus.emit('switchAnsType', { ansType: Paper.ansType });
    }

    /**
     * @name: 批改模式切换
     */
    const correctChange = async (value: number, mode: '' | 'AB' = '') => {
      state.Paper.setCorrectType(value);

      if (ICorrectType.PHOTO == value) {
        mergeQuesBefore = Paper.mergeQues;
        state.cardType = ICARDMODEL.BLANKCARD;
        switchMergeQues(1)
        // 拍改模式
        Paper.switch2PhtotMode();
        // Paper.updateAndMergeQuestions(state.quesInfo);
        bus.emit('switchCorrect', { correctType: Paper.correctType });

        return
      }

      //避免影响网手阅切换导致合并状态重置，拍改卡目前不用支持网手阅切换，暂时注释此代码
      // 恢复之前的合并状态
      // if (mergeQuesBefore != state.Paper.mergeQues) {
      //   state.Paper.mergeQues = mergeQuesBefore;
      //   switchMergeQues(mergeQuesBefore);
      // }

      bus.emit('switchCorrect', { correctType: Paper.correctType });

      await nextTick();
      setTimeout(async () => {
        await nextTick();
        bus.emit('renderAllCard');
      }, 100);
      // Paper.notifyUpdateAllData()
      if (mode != 'AB') {
        const data = {
          from: Paper.abType,
          type: 'updateInfos',
          func: 'correctChange',
          arguments: [value],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
    };

    /**
     * @name: 题目合并
     */
    const switchMergeQues = async (value: number) => {
      if (value === 0) {
        Paper.splitObjectiveQuestions();
      } else {
        Paper.mergeObjectiveQuestions();
      }
      await nextTick();
      bus.emit('switchMergeQues');
      bus.emit('updateQuesInfos');
    };

    /**
     * @description: 更改考号类型
     */
    const changeNumberLayout = (val: number) => {
      Paper.changeNumberLayout(val);
      bus.emit('reRenderMathJax');
    };

    /**
     * @name:添加题目
     */
    const addNewQues = async (data: any) => {
      let qIndex = Paper.getBigQuesLength();
      if (data.type == QUES_TYPE.mixin) {
        // 添加混合题
        state.mixinDialogMode = 'add';
        state.mixinDataIndex = qIndex;
        state.showMixinDialog = true;
      } else if (data.type == QUES_TYPE.noAnswerArea) {
        // 添加非做题区域
        let lastIndex = Paper.getQuesInfos().length;
        Paper.updateAndMergeQuestions(lastIndex, {
          id: generateUUID(),
          name: '非作答区',
          type: '非作答区',
          quesScore: 5,
          typeId: QUES_TYPE.noAnswerArea,
          score: 0,
          count: 1,
          height: '30mm',
          data: [
            {
              quesNos: '',
              score: 0,
              name: '',
              typeId: QUES_TYPE.noAnswerArea,
              id: generateUUID(),
              type: '',
              answer: '',
              optionCount: 4,
              isWriting: false
            }
          ],
        });
        state.refreshKey++;
        await nextTick();
        setTimeout(function () {
          bus.emit('updateQuesInfos');
        }, 10);
      } else {
        // 其他题型
        state.singleDialogMode = 'add';
        state.singleQuesType = data.type;
        state.singleIsWriting = data.isWriting;
        state.singleDataIndex = qIndex;
        state.showSingleDialog = true;
      }
    };

    /**
     * @name 校验填空题线上批改设置完成情况
    */
    const getErrorFillLine = () => {
      const quesList = Paper.getQuesInfos();
      let errList = [];
      
      // 处理答案文本
      const processAnswers = (item, lineList) => {
        if (lineList.length > 1) {
          let answers = "";
          if(Array.isArray(item.answer)){
            answers = item.answer;
          }else{
            let ans = item.answer.split(/(?<!&(?:[a-zA-Z0-9]+|#[0-9]+|#x[0-9A-Fa-f]+))(?:;|；)/);
            if(ans.length <= 1){
              ans = item.answer.split(/\s{3,}/);
            }
            answers = ans;
          }
          
          lineList.forEach((line, index) => {
            line.answer = getHtmlText(answers[index] || '');
          });
        } else if (lineList.length === 1) {
          lineList[0].answer = getHtmlText(item.answer || '');
        }
      };
      
      // 检查分数和空数是否有误
      const hasScoreError = (item) => {
        const totalScore = item.lineList.reduce((sum, line) => 
          (Math.round(sum * 100 + Number(line.score) * 100) / 100), 0);
          
        return item.score != totalScore || 
               item.lineList.find(line => isNaN(Number(line.score))|| line.score == '') || 
               !item.lineList?.length;
      };
      
      // 筛选填空题
      const isFillQuestion = (item) => 
        (item.typeId == QUES_TYPE.fill || item.typeId == QUES_TYPE.fillEva) && 
        ((item.markType == MARK_TYPE.ONLINE && Paper.correctType == ICorrectType.HAND) || item.scanMode == QUES_SCAN_MODE.AI_FILL);
      
      for (const item of quesList) {
        if (!isFillQuestion(item)) continue;
        
        const list = (item.children?.length && item.children) || 
                     (item.data?.length && item.data) || [];
        
        const errQues = list.filter(ques => {
          if (ques.mixinMode) return false;
          
          if (ques.children?.length || ques.data?.length) {
            // 非混合题的主题处理
            const subQuestions = ques.children || ques.data;
            const errSq = subQuestions.find(sq => {
              processAnswers(sq, sq.lineList);
              return hasScoreError(sq);
            });
            
            return !!errSq;
          } else {
            // 普通题目处理
            processAnswers(ques, ques.lineList);
            return hasScoreError(ques);
          }
        });
        
        if (errQues.length) {
          const names = errQues
            .filter(item => !item.mixinMode)
            .map(item => item.quesNos);
          
          errList = errList.concat(names);
        }
      }
      return errList;
    };

    /**
     * @name: 保存
     * @param saveType 0:保存 1:预览 2：布置/完成
     */
    const saveEvent = async (saveType: number, mode: '' | 'AB' = '') => {
      //如果没有编辑模板卡权限 不进行数据保存
      if (Paper.editerType == IEditerPowerType.TPL_NO_POWER)
        return;
      if (mode != 'AB') {
        const data = {
          from: Paper.abType,
          type: 'updateInfos',
          func: 'saveEvent',
          arguments: [saveType],
        }
        window.parent.postMessage(JSON.stringify(data), '*');
      }
      return new Promise((resolve, reject) => {
        bus.emit('setQueHeight');

        setTimeout(async () => {
          let params = {} as any;
          if (Paper.isEdit) {
            //编辑只保存题目数据
            params = {
              paperNo: Paper.paperId,
              quesInfo: Paper.getSaveCardContent(),
            };
          } else {
            params = Paper.exportSaveJSON();
            if (saveType == 2) {
              params.updateTeamInfo = 1;
            }
          }
          params.optUserId = Paper.userId;
          if (isPhotoCorrect.value) {
            params.originCardType = 5;
            params.source = 1;
          }
          const result = await updateViewPaper({ json: JSON.stringify(params) });

          //点击触发显示提示，避免发送操作提示过多
          if (result.code == 1) {
            resolve(result);
            if (saveType == 0) {
              ElMessage({
                message: '保存成功',
                type: 'success',
              });
            }
          }
          if (result && result.code !== 1) {
            reject(result);
            ElMessage({
              message: result.msg,
              type: 'error',
            });
          }
        }, 200);
      });
    };

    /**
     * @name: 下载
     */
    const downloadEvent = async (name: string) => {
      const loading = ElLoading.service({
        lock: true,
        text: '答题卡导出中',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      const res = await examCard({
        paperId: Paper.paperId,
        type: Paper.pageLayout == IPAGELAYOUT.A4 ? 'A4' : 'A3',
        isSend: 1,
        pageCount: Paper.pageCount,
      });
      if (res.code == 1) {
        base64toFile(res.data, `${name}.pdf`);
        loading.close();
        return true
      } else {
        loading.close();
        return false
      }
    };

    /**
     * @name: 预览
     */
    const preview = async () => {
      await saveEvent(1);
      //预览增加延迟，避免预览和制卡页面内容重复保存
      setTimeout(() => {
        ctx.emit('preview');
      }, 200);
    };

    /**
     * @name: 布置
     */
    const assignEvent = async () => {
      state.sendParam.info = {
        paperId: Paper.paperId,
        name: Paper.name,
        subjectId: Paper.subjectId,
        gradeCode: Paper.gradeId,
      };
      state.sendParam.isShow = true;
    };
    /**
     * @name: 关闭布置弹框
     */
    const closeSendDialog = async (obj: any) => {
      if (obj.isSend) {
        await downloadEvent(obj.name);
        window.location.replace('about:blank');
        window.close();
      }
      state.sendParam.isShow = false;
    };

    const customHeader = () => {
      ElMessageBox.prompt('', '设置页眉', {
        inputValue: Paper.headerName,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(({ value }) => {
        Paper.headerName = value;
        bus.emit('updateHeaderName', value);
        ElMessage({
          type: 'success',
          message: `设置成功`,
        });
      });
    };

    const openOverCardToast = type => {
      const errorData = getErrorFillLine();
      if (errorData.length) {
        ElMessageBox.alert(
          `填空题第${errorData.join(',')}题总分与小空分之和不一致，请修改对应小题分数后再保存！`,
          '提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
          }
        )
        return;
      }
      let msg = '完成后答题卡版式、内容不支持修改，可先下载预览版进行确认！是否确定完成？';
      if (Paper.abType) {
        msg = '完成后A、B卡均会下载打印版本，且完成后A、B卡的版式、内容均不支持修改，可先下载预览版本进行确认！是否确定完成？';
      }
      ElMessageBox.confirm(
        msg,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          const res: any = await saveEvent(2);
          if (res.code == 1) {
            if (type == 1) {
              completeCard();
            } else {
              assignEvent();
            }
          } else {
            ElMessage({
              type: 'error',
              message: `数据保存失败`,
            });
          }
        })
        .catch(() => { });
    };
    /**
     * @name:完成制卡
     */
    const completeCard = async (mode: '' | 'AB' = '') => {
      const tbid = route.query.id;
      let isBreak = false;
      const res = await overCard({
        schoolId: Paper.schoolId,
        tbId: tbid,
        optUserId: Paper.userId,
      });
      if (res.code == 1) {
        if (route.query.relateCard) {
          let params = {
            schoolId: Paper.schoolId,
            examId: route.query.examId,
            personalBookId: route.query.personBookId,
            relateCardType: route.query.relateCardType
          };
          if (Number(route.query.relateCardType) == ICARD_STATE.abCard) {
            if (Paper.abType == 'A') {
              params['tbId'] = tbid;
              params['paperNo'] = Paper.paperId;
            } else {
              params['tbId1'] = tbid;
              params['paperNo1'] = Paper.paperId;
            }
          } else if (Number(route.query.relateCardType) == ICARD_STATE.abPaperTwo) {
            if (Number(route.query.abCardSheetType) == IAB_CARD_SHEEFT_TYPE.aCard) {
              params['tbId'] = tbid;
              params['paperNo'] = Paper.paperId;
            } else {
              params['tbId1'] = tbid;
              params['paperNo1'] = Paper.paperId;
            }
          } else {
            params['tbId'] = tbid;
            params['paperNo'] = Paper.paperId;
          }
          const relateRes = await relateAnswerCard(params);
          if (relateRes.code != 1) {
            isBreak = true;
            ElMessage({
              message: relateRes.msg,
              type: 'error',
              customClass: 'top-up-message'
            });
          }
        }
        const isSuccess = await downloadEvent(Paper.name);
        if (isSuccess) {
          if (mode != 'AB') {
            const data = {
              from: Paper.abType,
              type: 'updateInfos',
              func: 'completeCard',
              arguments: [],
            }
            window.parent.postMessage(JSON.stringify(data), '*');
            state.Paper.isEdit = true;
          } else {
            //通知父级页面完成制卡
            const data = {
              from: Paper.abType,
              type: 'completeCard',
            }
            window.parent.postMessage(JSON.stringify(data), '*');
            state.Paper.isEdit = true;
          }
          if (!Paper.abType && !isBreak) {
            //普通卡完成后关闭
            setTimeout(() => {
              window.location.replace('about:blank');
              window.close();
            }, 1000);
          }
        }
      }
    };

    //获取过滤后的小题数据
    const getFilterQuesSmallData = (smallData, quesIndex, smallIndex) => {
      const bigQues = state.Paper.quesInfos[quesIndex];
      if (!bigQues) return;
      const orginData = bigQues.data[smallIndex];
      smallData.judgeType = (orginData || smallData)?.judgeType;
      smallData.step = (orginData || smallData)?.step;
      smallData.gridPlace = (orginData || smallData)?.gridPlace;
      smallData.markType = (orginData || smallData)?.markType;
      smallData.gridType = (orginData || smallData)?.gridType;
      smallData.decimal = (orginData || smallData)?.decimal;
      smallData.lineType = (orginData || smallData)?.lineType;
      smallData.wordNumber = (orginData || smallData)?.wordNumber;
      smallData.cellSize = (orginData || smallData)?.cellSize;
      smallData.groupAlone = (orginData || smallData)?.groupAlone;
      if (bigQues.mixinMode) {
        //混合题处理
        smallData.data.forEach((sitem, index) => {
          sitem.judgeType = (orginData || smallData)?.judgeType;
          sitem.step = (orginData || smallData)?.step;
          sitem.gridPlace = (orginData || smallData)?.gridPlace;
          sitem.markType = (orginData || smallData)?.markType;
          sitem.gridType = (orginData || smallData)?.gridType;
        })
      }
      return smallData;
    }

    //获取过滤后的大题数据
    const getFilterQuesBigData = (bigData, quesIndex) => {
      const orginData = state.Paper.quesInfos[quesIndex];
      bigData.height = (orginData || bigData).height;
      bigData.judgeType = (orginData || bigData).judgeType;
      bigData.step = (orginData || bigData).step;
      bigData.gridPlace = (orginData || bigData).gridPlace;
      bigData.markType = (orginData || bigData).markType;
      bigData.gridType = (orginData || bigData).gridType;
      bigData.decimal = (orginData || bigData).decimal;
      bigData.lineType = (orginData || bigData).lineType;
      bigData.wordNumber = (orginData || bigData).wordNumber;
      bigData.cellSize = (orginData || bigData).cellSize;
      bigData.showType = (orginData || bigData).showType;
      bigData.verticalLines = (orginData || bigData).verticalLines;
      bigData.groupAlone = (orginData || bigData).groupAlone;
      bigData.data.forEach((item: any, index: number) => {
        item = getFilterQuesSmallData(item, quesIndex, index)
      });
      return bigData;
    }

    /**
     * @name 页面一开始加载
     */
    onMounted(async () => {
      window.addEventListener('message', async (event) => {
        try {
          const message = event.data;
          // console.log('接收到消息:', message);
          const methodName = message.func;
          if (message.type === 'updateQuestions') {
            if (methodName == 'updateAddQues') {
              const bigData = message.arguments[0];
              const quesIndex = message.arguments[1];
              getFilterQuesBigData(bigData, quesIndex);
              updateAddQues(bigData, quesIndex, 'AB');
            } else if (methodName == 'spliceQuestions') {
              Paper.spliceQuestions(message.arguments[0], message.arguments[1], message.arguments[2], 'AB');
            } else if (methodName == 'handleUpdateMixinQuestions') {
              const bigData = message.arguments[0];
              const quesIndex = message.arguments[1];
              getFilterQuesBigData(bigData, quesIndex);
              handleUpdateMixinQuestions(bigData, quesIndex, 'AB');
            } else if (methodName == 'handleUpdateSmallQuestions') {
              const smallData = message.arguments[0];
              const quesIndex = message.arguments[1];
              const smallIndex = message.arguments[2];
              getFilterQuesSmallData(smallData, quesIndex, smallIndex)
              handleUpdateSmallQuestions(smallData, quesIndex, smallIndex, 'AB');
            } else if (methodName == 'deleteQues') {
              deleteQues(message.arguments[0], 'AB');
            } else if (methodName == 'deleteSmallQues') {
              deleteSmallQues(message.arguments[0], message.arguments[1], 'AB');
            } else if (methodName == 'changeBigQuesPerScore') {
              const bigData = message.arguments[0];
              const quesIndex = message.arguments[1];
              getFilterQuesBigData(bigData, quesIndex);
              changeBigQuesPerScore(bigData, quesIndex, 'AB');
            } else if (methodName == 'changeSmallQuesSingleScore') {
              const bigData = message.arguments[0];
              const smallData = message.arguments[1];
              const quesIndex = message.arguments[2];
              getFilterQuesBigData(bigData, quesIndex);
              changeSmallQuesSingleScore(bigData, smallData, quesIndex, 'AB');
            } else if (methodName == 'changeAnswer') {
              const ques = message.arguments[0];
              const quesInfo = state.quesInfo;
              quesInfo.forEach(qItem => {
                if (qItem.typeId !== ques.typeId) return;

                qItem.data.forEach(subQItem => {
                  if (subQItem.id !== ques.id) return;

                  subQItem.answer = ques.answer;
                });
              });
            } else if (methodName == 'changeBigQuesName') {
              const ques = message.arguments[0];
              const quesInfo = state.quesInfo;
              quesInfo.forEach(qItem => {
                if (qItem.id != ques.id) return;
                qItem.fullname = ques.fullname;
                qItem.name = ques.name;
              });
              changeQuesName();
            } else if (methodName == 'changeSmallQuesName') {
              const ques = message.arguments[0];
              const quesInfo = state.quesInfo;
              quesInfo.forEach(qItem => {
                if (qItem.typeId !== ques.typeId) return;

                qItem.data.forEach(subQItem => {
                  if (subQItem.id !== ques.id) return;
                  subQItem.quesNos = ques.quesNos;
                });
              });
              changeQuesName();
            } else if (methodName == 'changeSmallMixQuesName') {
              const ques = message.arguments[0];
              changeSmallMixQuesName(ques)
            } else if (methodName == 'changeSmallQuesHalfScore') {
              const ques = message.arguments[1];
              const quesInfo = state.quesInfo;
              quesInfo.forEach(qItem => {
                qItem.data.forEach(subQItem => {
                  if (subQItem.id !== ques.id) return;
                  subQItem.halfScore = ques.halfScore;
                });
              });
            }
            await nextTick();
            // 通知更新
            state.refreshKey++;
          } else if (message.type == 'updateInfos') {
            if (methodName == 'changeSubject') {
              changeSubject(message.arguments[0], 'AB');
            } else if (methodName == 'correctChange') {
              correctChange(message.arguments[0], 'AB');
            } else if (methodName == 'saveEvent') {
              saveEvent(message.arguments[0], 'AB');
            } else if (methodName == 'completeCard') {
              completeCard('AB');
            }
          }
        } catch (error) {
          console.error('处理消息失败:', error);
        }
      });
      bus.on('editQues', (res: any) => handleEditView(res.item, res.index));
      bus.on('allCardRendered', () => {
        state.isLayoutChanging = false;
      });

      if (isPhotoCorrect.value) correctChange(ICorrectType.PHOTO)
    });

    /**
     * @name 取消完成制卡状态
     */
    const cancelOver = async () => {
      const updateParams = {
        tbId: Paper.tbId,
        optUserId: Paper.userId,
      };
      const updateRes = await updateNotOverCard(updateParams);
      const clearParams = {
        id: Paper.paperId,
        type: Paper.pageLayout == IPAGELAYOUT.A4 ? 'A4' : 'A3',
      };
      const clearRes = await clearPDFCache(clearParams);
      if (updateRes.code == 1 && clearRes.code == 1) {
        location.reload();
      } else {
        ElMessage({
          message: '请重试',
          type: 'error',
        });
      }
    };

    /**
     * @name 重置试卷数据
     */
    const resetPaper = async () => {
      const params = {
        tbId: Paper.tbId,
        optUserId: Paper.userId,
      };
      const res = await clearTestBankQueInfo(params);
      if (res.code == 1) {
        location.reload();
      } else {
        ElMessage({
          message: res.msg,
          type: 'error',
        });
      }
    };

    /**
     * @name  复制卡
    */
    const cloneCard = async () => {
      const res = await copyCardPaper({
        schoolId: Paper.schoolId,
        tbId: Paper.tbId,
        toUserId: Paper.userId
      });
      if (res && res.code == 1) {
        const data = res.data.data;
        const token = getQueryString("token");
        location.href = location.origin + location.pathname + `?id=${data.tbId}&token=${token}`;
      } else {
        ElMessage({
          message: res.msg,
          type: 'error',
        });
      }
    }

    /**
     * @name 切换所有列表折叠
     */
    // 所有列表是否折叠
    let allListCollapsed = false;
    const toggleAllListCollapsed = () => {
      allListCollapsed = !allListCollapsed;

      state.quesInfo.forEach(item => {
        item.collapsed = allListCollapsed;
      });
    };

    const onDragEnd = () => {
      if (state.isBlankCard) {
        let sortedData = [];
        let parents = state.quesInfo.filter(ele => {
          return !ele.parentId;
        });
        parents.forEach((item, index) => {
          sortedData.push(item);
          let items = state.quesInfo.filter(ele => {
            return item.id == ele.parentId;
          });
          sortedData.push(...items);
        });
        // 更新状态中的data
        state.quesInfo = sortedData;
      }

      Paper.updateAndMergeQuestions(state.quesInfo);
      bus.emit('orderQuesInfos');
      Paper.notifyUpdateAllData();
    };

    return {
      ...toRefs(state),
      route,
      isHandCorrect,
      isPhotoCorrect,
      getColWidth,
      isShowAB,
      changeFont,
      convertSort,
      toggleAllListCollapsed,
      changeBigQuesPerScore,
      changeSmallQuesSingleScore,
      changeSmallQuesHalfScore,
      changeQuesName,
      changeBigQuesName,
      changeSmallQuesName,
      changeSmallMixQuesName,
      closeDialog,
      saveEvent,
      downloadEvent,
      assignEvent,
      handleInput,
      closeSendDialog,
      preview,
      cardChange,
      answerPrefix,
      changeAnswerType,
      layoutChange,
      correctChange,
      switchMergeQues,
      addNewQues,
      customHeader,
      handleTFChange,
      handleChoiceChange,
      checkScoreRules,
      updateAddQues,
      handleUpdateMixinQuestions,
      handleUpdateSmallQuestions,
      deleteQuesConfirm,
      deleteSmallQuesConfirm,
      completeCard,
      openOverCardToast,
      openMixinEditView,
      handleEditView,
      handleEditSmallView,
      changeNumberLayout,
      changeSubject,
      changeStuNo,
      cancelOver,
      resetPaper,
      cloneCard,
      onDragEnd,
    };
  },
});
</script>

<style lang="scss" scoped>
.c30-teach-card-silder-container {
  position: relative;
  height: 100%;
  overflow-x: hidden;

  .c30-teach-card-silder-main {
    position: relative;
    font-size: 14px;
    height: 100%;
    padding-bottom: 60px;
    overflow-y: scroll;
    overflow-x: hidden;

    .silder-title-bar {
      position: relative;
      height: 30px;
      line-height: 30px;
      font-size: 18px;
      font-weight: 400;
      color: #000000;
      padding: 0 24px;
      margin-top: 5px;
    }
    
    .sticky-title{
      position: sticky;
      top: 0;
      z-index: 999;
      background: #fff;
      /* 确保在不同浏览器中的兼容性 */
      -webkit-position: sticky;
      /* 添加过渡效果，使吸顶更平滑 */
      transition: box-shadow 0.2s ease-in-out;
    }

    .dotbot {
      position: absolute;
      left: 24px;
      bottom: 0;
      background: url('../../assets/bg.png');
      width: 50px;
      height: 12px;
    }

    .full-score {
      font-size: 16px;
      color: #3d3d3d;
      margin-left: 5px;
      float: right;
    }

    .silder-content {
      padding: 0 24px;
      margin-top: 5px;

      .layout-tips {
        font-size: 3.5mm;
        line-height: 3.5mm;
        text-align: center;
      }

      .subject-tag {
        position: relative;
        top: 3px;
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }

    .icon {
      width: 24px;
      height: 24px;
      display: inline-block;
      vertical-align: middle;
    }

    .edit {
      background: url('../../assets/icon_bianji.png') 100% 100%;
      margin-right: 10px;
    }

    .edit-small {
      display: none;
    }

    .remove {
      background: url('../../assets/icon_delete.png') no-repeat 0 0;
    }

    .ques-title {
      .el-input {
        width: 100% !important;
      }
    }

    .ques-score {
      .el-input {
        width: 50px !important;
        margin: 0 4px;
      }
    }

    .small-ques-row {
      margin-bottom: 5px;

      .remove {
        display: none;
      }
    }

    .el-row {
      height: 30px;
      line-height: 30px;

      &:hover {

        .edit-small,
        .remove {
          display: inline-block;
        }
      }
    }

    .el-col {
      text-align: center;
    }

    .el-input {
      width: 70px !important;
    }

    .el-input__inner {
      padding: 1.2mm 0 !important;
    }

    .ques-list {
      margin-top: 12px;
      padding: 0 12px;

      .big-ques-row {
        position: relative;
        height: auto;
        line-height: 50px;
        padding-left: 13px;
        padding-bottom: 2px;
        background-color: #f4f4f4;
        border-top: 1px solid #f4f4f4;
        border-bottom: 1px solid #f4f4f4;
        // transition: background-color linear .3s;

        &.collapsed {
          background-color: transparent;

          &:hover {
            background-color: #f4f4f470;
          }
        }

        &.no-bg {
          background-color: transparent;
        }

        >.el-col {

          &.ques-title,
          &.ques-score,
          &.ques-handle {
            z-index: 10;
          }

          position: relative;
        }

        .ques-title--cover {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 1;

          &::before {
            content: '';
            opacity: 0;
            position: absolute;
            top: 5px;
            left: 0;
            bottom: 5px;
            width: 4px;
            z-index: 10;
            background-color: #3399ff9d;
            transition: opacity linear 0.2s;
          }

          &:hover {
            &::before {
              opacity: 1;
            }
          }
        }
      }

      .big-ques-list {
        padding: 5px 0 10px;
      }
    }
  }

  .footer-btn {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 100;
    padding: 10px 0;
    background-color: #fff;
    width: 100%;
    justify-content: center;

    .el-col {
      justify-content: center;
      display: flex;
    }
  }

  .add-ques-list {
    display: flex;
    padding: 10px 24px;
    flex-wrap: wrap;
    height: auto !important;

    .add-ques {
      width: 91px;
      margin-top: 8px;
      padding: 17px 0;
      font-size: 15px;
      border: 1px solid #cbced1;

      ::v-deep span {
        display: inline-block;
        vertical-align: middle;
      }

      &:hover {
        background-color: #3399ff;
        color: #fff;
      }
    }
  }

  .answer-select {
    width: 70px;

    ::v-deep {
      .el-input__suffix {
        display: none;
      }
    }
  }
}
</style>
<style lang="scss">
.c30-teach-card-silder-main,
.ques-list {
  .silder-content {
    .el-form-item {
      margin-bottom: 5px;
    }
  }

  .el-input__inner {
    height: 30px !important;
    line-height: 30px !important;
    padding: 1.2mm 1mm !important;
    text-align: center;
  }

  .el-radio {
    margin-right: 30px;
  }

  .layout-box {
    .el-radio {
      margin: 5px 25px 5px 0;
    }
  }
}

.top-up-message {
  z-index: 9999;
}
</style>
