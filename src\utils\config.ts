/*
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2023-10-27 18:16:26
 * @LastEditors: liuyue <EMAIL>
 */
import { INUMBERLAYOUT, IPAGELAYOUT } from "@/typings/card";
import Paper from "@/views/paper";

// 准考证号时头部高度
export const normalHeight = 69
// 条形码时头部高度
export const lowHeight = 60
// 手写考号+密封线
export const writeHeight = 48
// 二维码
export const qrcodeHeight = 32
// 二维码+密封性
export const qrcodeSealHeight = 25
// 获取动态头部高度
export function getHeaderInfoHeight() {
    if(Paper.numberLayout == INUMBERLAYOUT.WRITE){
        return writeHeight
      }else if(Paper.numberLayout == INUMBERLAYOUT.TICKET){
        return normalHeight;
      }else if(Paper.numberLayout == INUMBERLAYOUT.QRCODE || Paper.numberLayout == INUMBERLAYOUT.MORE){
        if(Paper.isSealLine || Paper.pageLayout == IPAGELAYOUT.A3  || [IPAGELAYOUT.A4,IPAGELAYOUT.A42].includes(Paper.pageLayout)){
          return qrcodeSealHeight
        }else{
          return qrcodeHeight
        }
      }else{
        return lowHeight;
      }
}

/**
 * @name: 页面高度
 */
export const pageHeight = 297
/**
 * @name: 底部预留高度
 */
export const footerKeepHeight = 14
/**
 * @name: 试卷信息高度
 */
export const headerInfoHeight = 65
/**
 * @name: 各类布局页面宽度集合
 */
export const  pageWidths = {
    A4_0: {
        width: 210,
        padding: 16
    },
    A4_1: {
        width: 210,
        padding: 24
    },
    A3_2_0: {
        width: 198,
        padding: 3
    },
    A3_2_1: {
        width: 177,
        padding: 1
    }
}