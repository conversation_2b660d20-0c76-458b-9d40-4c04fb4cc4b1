<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title></title>
  <link rel="stylesheet" href="./font/iconfont.css" />
  <link rel="stylesheet" href="./styles/photo-correct.css" />
  <script>
    /*全局答应缩放*/
    window.PRINT_ZOOM = 4;
  </script>
  <style>
    #app {
      width: 100%;
      height: 100%;
      transform-origin: 0 0;
    }

    @media print {
      #app {
        zoom: 4;
        transform: none !important;
      }
    }
    u {
      border-bottom: 1px solid #000;
      text-decoration: none;
    }

    #app.card-zoom .header,
    #app.card-zoom .preview-header,
    #app.card-zoom .headerinfo .card-color-info,
    #app.card-zoom .headerinfo .notice-info,
    #app.card-zoom .headerinfo .full-wrap,
    #app.card-zoom .headerinfo .stu-num,
    #app.card-zoom .headerinfo .stu-num-td,
    #app.card-zoom .headerinfo .stu-tb-left,
    #app.card-zoom .headerinfo .empty-th,
    #app.card-zoom .headerinfo .full-example,
    #app.card-zoom .ques-box-container::after,
    #app.card-zoom .split-line::after,
    #app.card-zoom .split-line.split-line--subject,
    #app.card-zoom .ques-box,
    #app.card-zoom .score-table,
    #app.card-zoom .score-table td,
    #app.card-zoom .mark-list .mark-item,
    #app.card-zoom .line-list .underline,
    #app.card-zoom .preview .preview-warp .preview-header,
    #app.card-zoom .ques-noanswerarea .ques-box {
      border-width: 1px;
    }

    #app.card-zoom .dotWrapper u {
      text-decoration-thickness: 1px;
    }

    .q-opt+.q-opt.ques-noanswerarea .ques-box,
    .q-opt+.q-opt .ques-box-container::after,
    .q-opt.is-big-mixin+.q-opt .ques-box-container .score-table,
    .q-opt+.q-opt .ques-box-container .score-table,
    .q-item-box6+.q-item-box6 .score-table,
    .q-item-box6+.q-item-box6 .ques-box,
    .no-top+.ques-box {
      border-top-color: transparent !important;
    }

    .q-opt.is-big-mixin+.q-opt .ques-box-container::after,
    .q-opt+.q-opt .ques-content-name+.splitnode-container .ques-box-container::after,
    .q-opt+.q-opt .page-splitor+.ques-box-container::after,
    .q-opt+.q-opt .ques-content-name+.splitnode-container .ques-box-container .score-table,
    .q-opt+.q-opt .page-splitor+.ques-box-container .score-table {
      border-top-color: #000 !important;
    }
    .quescard .q-item-box8 + .q-opt.ques-noanswerarea .ques-box, 
    .quescard .q-item-box8 + .q-opt.ques-noanswerarea .score-table, 
    .quescard .q-item-box8 + .q-opt.ques-noanswerarea::after, 
    .quescard .q-item-box8 + .q-opt .ques-box-container .ques-box, 
    .quescard .q-item-box8 + .q-opt .ques-box-container .score-table, 
    .quescard .q-item-box8 + .q-opt .ques-box-container::after, 
    .quescard .q-item-box1 + .q-opt.ques-noanswerarea .ques-box, 
    .quescard .q-item-box1 + .q-opt.ques-noanswerarea .score-table, 
    .quescard .q-item-box1 + .q-opt.ques-noanswerarea::after, 
    .quescard .q-item-box1 + .q-opt .ques-box-container .ques-box, 
    .quescard .q-item-box1 + .q-opt .ques-box-container .score-table, 
    .quescard .q-item-box1 + .q-opt .ques-box-container::after, 
    .quescard .q-item-box2 + .q-opt.ques-noanswerarea .ques-box, 
    .quescard .q-item-box2 + .q-opt.ques-noanswerarea .score-table, 
    .quescard .q-item-box2 + .q-opt.ques-noanswerarea::after, 
    .quescard .q-item-box2 + .q-opt .ques-box-container .ques-box, 
    .quescard .q-item-box2 + .q-opt .ques-box-container .score-table, 
    .quescard .q-item-box2 + .q-opt .ques-box-container::after,
    .quescard .q-item-box3 + .q-opt.ques-noanswerarea .ques-box, 
    .quescard .q-item-box3 + .q-opt.ques-noanswerarea .score-table, 
    .quescard .q-item-box3 + .q-opt.ques-noanswerarea::after {
      border-top-color: #000 !important;
    }

    .q-opt .imglist-content {
      position: absolute !important;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1000;
    }

    .q-opt .imglist-content>img {
      pointer-events: auto;
      max-width: 100%;
      max-height: 100%;
    }

    .dis-flex {
      display: flex;
    }

    html {
      width: 100%;
      height: 100%;
    }

    body {
      margin: 0;
      padding: 0;
      background-color: #616161;
      width: 100%;
      height: 100%;
      overflow: auto;
      color: #000;
    }

    table {
      display: table;
      box-sizing: border-box;
      text-indent: initial;
      border-spacing: 2px;
      border-color: gray;
    }

    html {
      color: #454545;
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
    }

    body,
    div,
    dl,
    dt,
    dd,
    ul,
    ol,
    li,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    pre,
    code,
    form,
    fieldset,
    legend,
    input,
    textarea,
    p,
    blockquote,
    th,
    td,
    hr,
    button,
    article,
    aside,
    details,
    figcaption,
    figure,
    footer,
    header,
    hgroup,
    menu,
    nav,
    section {
      margin: 0;
      padding: 0;
    }

    article,
    aside,
    details,
    figcaption,
    figure,
    footer,
    header,
    hgroup,
    menu,
    nav,
    section {
      display: block;
    }

    body,
    html,
    #app {
      width: 100%;
      height: 100%;
    }

    body,
    button,
    input,
    select,
    textarea {
      font: 12px/1.5 tahoma, 微软雅黑, 宋体;
    }

    input,
    select,
    textarea {
      font-size: 100%;
    }

    table {
      border-collapse: collapse;
      border-spacing: 0;
    }

    th {
      text-align: inherit;
    }

    fieldset,
    img {
      border: 0;
    }

    iframe {
      display: block;
    }

    abbr,
    acronym {
      border: 0;
      font-variant: normal;
    }

    del {
      text-decoration: line-through;
    }

    .clearfix:before,
    .clearfix:after {
      display: table;
      content: " ";
    }

    .clearfix:after {
      clear: both;
    }

    .pull-left {
      float: left;
    }

    .pull-right {
      float: right;
    }

    /* address,
      caption,
      cite,
      code,
      dfn,
      em,
      th,
      var {
        font-style: normal;
        font-weight: 500;
      } */

    ol,
    ul {
      list-style: none;
    }

    caption,
    th {
      text-align: left;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      font-size: 100%;
      font-weight: 500;
    }

    q:before,
    q:after {
      content: "";
    }

    sub,
    sup {
      font-webkit-scrollbar-tracksize: 75%;
      line-height: 0;
      position: relative;
      vertical-align: baseline;
    }

    sup {
      top: -0.5em;
    }

    sub {
      bottom: -0.25em;
    }

    a:hover {
      text-decoration: underline;
    }

    ins,
    a {
      text-decoration: none;
    }

    .middle-helper>*,
    .middle-helper:before,
    .bottom-helper>*,
    .bottom-helper:before {
      display: inline-block;
      vertical-align: middle;
    }

    .middle-helper:before,
    .bottom-helper:before {
      width: 0;
      height: 100%;
      content: "";
    }

    .middle-helper>*,
    .middle-helper:before {
      vertical-align: middle;
    }

    .bottom-helper>*,
    .bottom-helper:before {
      vertical-align: bottom;
    }

    .ver-bot {
      vertical-align: bottom;
    }

    .header {
      padding: 0;
      margin: 0;
      margin-bottom: 1mm;
      border-top: 0.1mm solid #fff;
      border-bottom: 0.1mm solid #000;
      position: relative;
      width: 100%;
      font-family: 宋体, SimSun, Times New Roman;
    }

    .water-mark {
      display: none;
      position: fixed;
      color: red;
      top: 150mm;
      left: 130mm;
      font-size: 72px;
      z-index: 999;
      transform: rotate(-330deg);
      opacity: 0.2;
    }

    .a4 .water-mark {
      left: 30mm;
    }

    .header-table {
      width: 100%;
      display: flex;
      padding-top: 2mm;
    }

    .header-table.only {
      display: block;
      text-align: center;
    }

    .a33 .header-table {
      padding-top: 0mm;
      /* height: 57mm; */
    }

    .header-table .left {
      width: 110mm;
    }

    .header-table .right {
      /* width: calc(100% - 110mm); */
      position: relative;
    }

    .header-table tr {
      width: 100%;
    }

    .header-table td {
      vertical-align: inherit;
    }

    .header-table .header-info {
      display: flex;
      /* margin-top: 2mm; */
      align-items: center;
    }

    .header-table .headerinfo {
      position: relative;
    }

    .header-name {
      margin: 0;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      height: 100%;
      -ms-flex-pack: distribute;
      justify-content: space-around;
      max-height: 21mm;
      width: 100%;
    }

    .name-txt {
      display: block;
      font-size: 4.3mm;
      max-width: 100%;
      margin: 0 auto;
      font-weight: 700;
      word-break: break-all;
      text-align: center;
      overflow: hidden;
      -webkit-line-clamp: 1;
      line-height: 6.5mm;
      min-height: 6.5mm;
      font-family: 宋体, SimSun, Times New Roman;
    }

    .flex-end {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: end;
      -ms-flex-pack: end;
      justify-content: flex-end;
    }

    .stu-info-wrap.m-b-4 {
      margin-bottom: 4mm;
    }

    .a33 .stu-info-wrap.m-b-4 {
      margin-bottom: 0;
    }

    .flex-center {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      justify-content: center;
    }

    .barcode-wrapper {
      width: 65mm;
    }

    .stu-bar-code-container {
      width: 100%;
      height: 30mm;
      padding: 5mm;
      border: 1px dashed #000;
    }

    .stu-bar-code {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
      width: 100%;
      height: 100%;
      background-color: #e4e4e4;
    }

    .stu-bar-code .text {
      color: #a5a5a5;
    }

    .stu-bar-code .text--top {
      font-size: 20px;
    }

    .stu-bar-code .text--bottom {
      font-size: 12px;
    }

    .stu-no-table {
      border-collapse: collapse;
      padding: 0;
      margin: 0 auto;
      text-align: center;
      color: #000;
    }

    .stu-no-first-tr {
      height: 4mm;
    }

    .stu-num {
      width: 30mm;
      font-size: 3mm !important;
      border: 0.1mm solid #000;
      border-collapse: collapse;
      text-align: center;
    }

    .stu-no-second-tr {
      height: 5mm;
    }

    .stu-no-three-tr {
      margin: 0;
      padding: 0;
    }

    .stu-tb-left {
      border: 1px solid #000;
      border-collapse: collapse;
      padding-top: 0;
      border-bottom-color: #000;
      width: 6.6mm;
      color: #000;
      border-left-color: #000;
    }

    .stu-num-td {
      border: 1px solid #000;
      border-collapse: collapse;
      padding-top: 0;
      border-bottom-color: #000;
      width: 6.6mm;
      color: #000;
    }

    .stu-num-td:last-child {
      border-right-color: #000;
    }

    .td-flex {
      -webkit-box-flex: 1;
      -ms-flex: 1;
      flex: 1;
    }

    .table-left-td {
      height: 100%;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    .table-notice-div {
      width: 100%;
    }

    .table-left-td,
    .table-notice-div {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -ms-flex-pack: distribute;
      justify-content: space-around;
    }

    .stu-info-wrap {
      padding-top: 2mm;
      text-align: center;
      position: relative;
      font-size: 3.5mm !important;
    }

    .notice-info {
      display: inline-block;
      width: 100%;
      height: 21mm;
      max-width: 90mm;
      text-align: left;
      border: 0.1mm solid #000;
      line-height: 4mm;
      padding: 0;
      margin: 0;
      word-break: break-all;
      overflow: hidden;
    }

    .notice-title {
      text-align: center;
      font-size: 3.5mm;
      margin: 0;
      padding: 0;
      color: #000;
    }

    .notice-infop {
      margin: 0 1mm;
      padding: 0;
      color: #000;
    }

    .card-color {
      color: #000;
    }

    .full-wrap {
      margin-top: 1mm;
      max-width: 90mm;
      width: 100%;
      line-height: 7mm;
      font-size: 3mm !important;
      border: 0.1mm solid #000;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      -ms-flex-pack: distribute;
      justify-content: space-around;
    }

    .a33 .full-wrap {
      margin-top: 5mm;
    }

    .full-item {
      vertical-align: middle;
    }

    .full-item span {
      margin-right: 1mm;
    }

    .full-sec {
      display: inline-block;
      border-width: 1.5mm 2.5mm;
      border-style: solid;
      border-color: #000;
      width: 0;
      height: 0;
    }

    .full-example {
      margin: 0;
      padding: 0;
      width: 5mm;
      height: 3mm;
      border: 0.1mm solid #000;
      font-size: 5mm;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -ms-flex-pack: distribute;
      justify-content: space-around;
    }

    .stu-num-full-sec {
      position: relative;
      /* display: inline-block; */
      line-height: 4.1mm;
      vertical-align: bottom;
      border: 0 solid #000;
      width: 6.6mm;
      height: 4.1mm;
      padding: 0;
      margin: 0;
      font-size: 2.5mm !important;
      font-family: 宋体, STSong, SimSun;
    }

    .stu-num-txt {
      margin: 0;
      padding: 0 0.6mm;
    }

    .empty-th {
      border: 0.1mm solid #000;
      border-collapse: collapse;
      font-size: 2.5mm !important;
    }

    .preview {
      /* min-height: 297mm; */
      margin: 0 auto;
      /* padding: 0 2mm; */
      background: #fff;
      /* overflow: hidden; */
    }

    .preview.a4 {
      width: 192mm;
    }

    .preview.a3 {
      width: 392mm;
    }

    .preview.a33 {
      width: 398mm;
    }

    .preview.a4 .hand-write-div.left {
      left: -12.5mm;
    }

    .preview.a3 .hand-write-div.left {
      left: -14mm;
    }

    .preview.a33 .hand-write-div.left {
      left: -14mm;
    }

    .preview.a4 .hand-write-div.right {
      right: -13mm;
    }

    .preview.a3 .hand-write-div.right {
      right: -14mm;
    }

    .preview.a33 .hand-write-div.right {
      right: -14mm;
    }

    .preview.a33 .surface-warp {
      width: 130mm !important;
    }

    .preview.a3 .preview-warp {
      /* height: 296.85mm; */
      /* overflow-y: hidden; */
    }

    .preview.a33 .preview-warp {
      /* height: 296.86mm; */
      /* overflow-y: hidden; */
    }

    .preview-warp {
      /* height: 297.13mm; */
      height: 297.38mm;
      /* overflow-y: hidden; */
      position: relative;
    }

    .preview-header {
      padding: 0;
      margin: 0;
      position: relative;
      width: 100%;
      height: 20mm;
      border-bottom: 0.1mm solid rgb(0, 0, 0);
      font-family: 宋体, SimSun, Times New Roman;
    }

    .card .preview-points .side-point {
      position: fixed;
      width: 4mm;
      height: 4mm;
      border-width: 2mm 2mm;
      border-style: solid;
      border-color: #000;
      background: #000;
      display: block;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }

    .preview-points .left-point {
      left: 4mm;
    }

    .preview-points .right-point {
      right: 4mm;
    }

    .a4 .preview-points .left-point {
      left: 2mm;
    }

    .a4 .preview-points .right-point {
      right: 2mm;
    }

    .a3 .preview-points .left-point {
      left: 7mm;
    }

    .a3 .preview-points .right-point {
      right: 7mm;
    }

    .preview-points .left-point1 {
      top: 75.5mm;
    }

    .preview-points .left-point2 {
      top: 145mm;
    }

    .preview-points .left-point3 {
      top: 214.5mm;
    }

    .preview-points .right-point1 {
      top: 75.5mm;
    }

    .preview-points .right-point2 {
      top: 145mm;
    }

    .preview-points .right-point3 {
      top: 214.5mm;
    }

    .preview-header .locatePoint-l {
      /* top: 7mm;
        left: -7mm; */
      /* width: 7mm;
        height: 3mm; */
      /* position: absolute; */
      /* background-color: black; */
      border-width: 1.5mm 3.5mm;
      border-style: solid;
      border-color: #000;
      display: block;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }

    .a4 .preview-header .locatePoint-l {
      position: fixed;
      top: 7mm;
      left: 2mm;
    }

    .a3 .preview-header .locatePoint-l {
      position: fixed;
      top: 7mm;
      left: 7mm;
    }

    .a33 .preview-header .locatePoint-l {
      position: fixed;
      top: 7mm;
      left: 4mm;
    }

    .preview-header .locatePoint-r {
      /* top: 7mm;
        right: -7mm; */
      /* width: 7mm;
        height: 3mm; */
      /* position: absolute; */
      /* background-color: black; */
      border-width: 1.5mm 3.5mm;
      border-style: solid;
      border-color: #000;
      display: block;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }

    .a4 .preview-header .locatePoint-r {
      position: fixed;
      top: 7mm;
      right: 2mm;
    }

    .a3 .preview-header .locatePoint-r {
      position: fixed;
      top: 7mm;
      right: 7mm;
    }

    .a33 .preview-header .locatePoint-r {
      position: fixed;
      top: 7mm;
      right: 4mm;
    }

    .preview-header .styles_left {
      left: 0;
    }

    .preview-header .styles_right {
      position: absolute;
      bottom: 0;
      padding: 0;
      margin: 0;
      font-size: 3.8mm;
      overflow: hidden;
      right: 0;
      min-width: 8mm;
    }

    .preview-header .styles_idAndNameBase {
      position: absolute;
      bottom: 1mm;
      padding: 0;
      margin: 0;
      font-size: 3.8mm;
      overflow: hidden;
      color: #3d3d3d;
    }

    .preview-content {
      /*尽量冗余版面高度内容 5mm*/
      height: 274mm;
      margin-bottom: -5mm;
      font-size: 0;
      overflow: hidden;
    }

    .preview-content .nbsp {
      height: 100%;
      display: inline-block;
      font-family: Arial, Helvetica, sans-serif;
    }

    .preview-content .surface-warp {
      height: 269mm;
      width: 192mm;
      display: inline-block;
      /* padding: 0 2mm; */
      vertical-align: top;
      font-size: var(--fontSize);
      font-family: var(--fontFamily);
      line-height: var(--lineHeight);
    }

    .preview-content .surface-warp .card-color-info {
      display: inline-block;
      width: 20mm;
      border-bottom: 0.1mm solid;
    }

    .a33 .preview-content .surface-warp .card-color-info {
      display: inline-block;
      width: 18mm;
      border-bottom: 0.1mm solid;
    }

    .a33 .preview-content .surface-warp .long-stu-no {
      display: block;
      align-items: start;
      margin-top: 0mm;
    }

    .a33 .preview-content .surface-warp .long-stu-no.hand {
      margin-top: 5mm;
    }

    .a33 .preview-content .surface-warp .long-stu-no.hand .stu-info-wrap {
      margin-bottom: 5mm;
    }

    .a33 .preview-content .surface-warp .long-stu-no .stu-info-wrap {
      text-align: left;
      padding-top: 0mm;
    }

    .a33 .preview-content .surface-warp .long-stu-no .card-color-info {
      width: 15mm;
    }

    .a33 .preview-content .surface-warp .long-stu-no #notice .full-wrap {
      width: 26mm;
      margin-top: 1mm;
      margin-bottom: 1mm;
    }

    .preview-content>.line {
      height: 100%;
      width: 8mm;
      display: inline-block;
      background-color: transparent;
    }

    .a33 .preview-content>.line {
      height: 100%;
      width: 4mm;
      display: inline-block;
      background-color: transparent;
    }

    .preview-footer {
      text-align: center;
      color: #000;
      display: flex;
      height: 14mm;
      width: 100%;
      justify-content: center;
      align-items: center;
      position: relative;
    }

    .a3 .preview-footer {
      height: 14mm;
    }

    .a33 .preview-footer {
      height: 14mm;
    }

    .preview-footer .locatePoint-l {
      /* top: 1mm;
        left: -7mm; */
      /* width: 3mm;
        height: 7mm; */
      /* position: absolute; */
      /* background-color: black; */
      border-width: 3.5mm 1.5mm;
      border-style: solid;
      border-color: #000;
      display: block;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }

    .a4 .preview-footer .locatePoint-l {
      position: fixed;
      bottom: 6mm;
      left: 2mm;
    }

    .a3 .preview-footer .locatePoint-l {
      position: fixed;
      bottom: 6mm;
      left: 7mm;
    }

    .a33 .preview-footer .locatePoint-l {
      position: fixed;
      bottom: 6mm;
      left: 4mm;
    }

    .preview-footer .locatePoint-l-new {
      border-width: 2mm 2mm;
      border-style: solid;
      border-color: #000;
      display: block;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }
    .a4 .preview-footer .locatePoint-l-new {
      position: fixed;
      bottom: 6mm;
      left: 6mm;
    }

    .a3 .preview-footer .locatePoint-l-new {
      position: fixed;
      bottom: 6mm;
      left: 11mm;
    }

    .a33 .preview-footer .locatePoint-l-new {
      position: fixed;
      bottom: 6mm;
      left: 8mm;
    }

    .preview-footer .locatePoint-r {
      /* top: 1mm;
        right: -7mm; */
      /* width: 3mm;
        height: 7mm; */
      /* position: absolute; */
      /* background-color: black; */
      border-width: 3.5mm 1.5mm;
      border-style: solid;
      border-color: #000;
      display: block;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }

    .a4 .preview-footer .locatePoint-r {
      position: fixed;
      bottom: 6mm;
      right: 2mm;
    }

    .a3 .preview-footer .locatePoint-r {
      position: fixed;
      bottom: 6mm;
      right: 7mm;
    }

    .a33 .preview-footer .locatePoint-r {
      position: fixed;
      bottom: 6mm;
      right: 4mm;
    }
    .preview-footer .locatePoint-r-new {
      border-width: 2mm 2mm;
      border-style: solid;
      border-color: #000;
      display: block;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }
    .a4 .preview-footer .locatePoint-r-new {
      position: fixed;
      bottom: 6mm;
      right: 6mm;
    }

    .a3 .preview-footer .locatePoint-r-new {
      position: fixed;
      bottom: 6mm;
      right: 11mm;
    }

    .a33 .preview-footer .locatePoint-r-new {
      position: fixed;
      bottom: 6mm;
      right: 8mm;
    }

    .preview-footer .page-tag {
      text-align: center;
      background: #d8d8d8;
      opacity: 1;
      margin-left: 3mm;
      padding: 1mm 2mm;
      border-radius: 4px 4px 4px 4px;
      font-size: 2.8mm;
    }

    .q-opt {
      width: 100%;
      position: relative;
      clear: left;
    }

    .q-opt.is-small-mixin+.is-small-mixin .ques-box-container {
      border-top: none;
    }

    .q-opt.is-small-mixin .score-table {
      /* border-top: 0.1mm solid #000; */
    }

    .q-opt.is-small-mixin .ques-content-name--small.is-small-subject {
      margin-bottom: 0;
    }

    .ques-content-name--small {
      padding: 0 3mm;
      line-height: 7mm;
    }

    .ques-content-name--small:empty {
      display: none;
    }

    .q-opt.q-item-box6 p {
      display: block;
    }

    .q-opt .ques-box {
      width: 100%;
      position: relative;
      padding: 0 1mm;
      /* overflow: hidden; */
    }

    .q-opt .ques-box.no-items,
    .q-opt .ques-box.no-items .ques-item-wrap,
    .q-opt .ques-item-wrap.no-items .ques-box {
      padding: 0;
    }

    .card .q-opt .ques-box {
      width: 100%;
      position: relative;
      /* padding: unset; */
    }

    .q-opt .ques-box .left {
      float: left;
      clear: left;
      min-width: 5mm;
    }

    .q-opt .ques-box .q-r-option.has-none-title {
      width: calc(100% - 5mm);
    }

    .q-opt .ques-box .q-r-option {
      display: inline-block;
      width: 100%;
    }

    .q-opt .ques-box .q-r-option .q-r-o-item {
      /* display: flex;
      align-items: center; */
      display: inline-block;
    }

    .q-item-box6 .ques-box {
      border: 0.1mm solid #000;
    }

    .ques-box img {
      vertical-align: middle;
      /* zoom: 0.8;; */
    }

    .ques-box .tb-dot {
      -webkit-text-emphasis: filled currentcolor;
      -webkit-text-emphasis-position: under right;
    }

    .ques-box .tb-wave {
      background-image: url("./wave.png");
      background-size: 11px 11px;
      background-position: 0 100%;
      background-repeat: repeat-x;
      padding-bottom: 6px;
      text-decoration: wavy;
    }

    .ques-box table {
      display: inline-table;
    }

    .q-opt .ques-box p {
      line-height: var(--lineHeight);
      min-height: var(--lineHeight);
    }
    .fontSizeTag .ques-box .n-opts{
      font-size: var(--fontSize);
    }

    .ocr-pos {
      /* outline: 2px solid red; */
      display: inline-block;
      height: 3mm;
      line-height: 3mm;
      text-align: center;
      padding: 0 1mm;
      font-size: 2.6mm;
    }

    .ocr-pos .optionChar {
      padding: 0 0.6mm;
      margin: 0;
    }

    .q-r-o-img {
      /* display: flex;
        justify-content: right; */
    }

    .q-item-box .op0 .q-r-o-item {
      margin-right: 5mm;
    }

    /* .q-item-box .op1 .q-r-o-item {
      flex: 1;
      width: calc(100%);
      min-width: calc(100%);
      max-width: calc(100%);
    }

    .q-item-box .op2 .q-r-o-item {
      flex: 1;
      width: 48%;
      min-width: 48%;
      max-width: 48%;
    }

    .q-item-box .op4 .q-r-o-item {
      flex: 1;
      width: 24%;
      min-width: 24%;
      max-width: 24%;
    }

    .q-item-box .op3 .q-r-o-item {
      flex: 1;
      width: 24%;
      min-width: 24%;
      max-width: 24%;
    } */

    .score-option-item {
      border: 0.2mm solid #000;
      border-left: 0.2mm;
      display: inline-block;
      width: 8mm;
      height: 6mm;
      font-size: 3mm;
      text-align: center;
      line-height: 6mm;
      margin: 0 0 0 0;
      vertical-align: bottom;
    }

    .mark-list {
      /* float: right; */
      position: absolute;
      bottom: 3mm;
      background: white;
      right: 3mm;
      margin: 0 0 0 1mm !important;
      z-index: 100;
    }

    .mark-list .mark-item {
      display: inline-block;
      border-top: 0.1mm solid #000;
      border-left: 0.1mm solid #000;
      border-bottom: 0.1mm solid #000;
      padding: 0 1mm !important;
      width: 8mm;
      height: 7mm;
      line-height: 7mm;
      text-align: center;
    }

    .mark-list .mark-item:last-child {
      border: 0.1mm solid #000;
    }

    .card .write-score-box {
      position: absolute;
      right: 0;
    }

    .write-score-box {
      float: right;
    }

    .write-score-box .write-score-item {
      width: 10mm;
      height: 12mm;
      margin-top: 1mm;
      margin-right: 1mm;
      border: 1px dashed #000;
      display: inline-block;
    }

    .write-score-box .score-dot {
      width: 2mm;
      display: inline-block;
      vertical-align: top;
      margin-top: 6mm;
    }

    .write-score-box .score-dot:before {
      content: "";
      display: inline-block;
      width: 1mm;
      height: 1mm;
      background: #000;
      border-radius: 50%;
    }

    table {
      border-collapse: collapse;
    }

    .score-table {
      width: 100%;
      border: 0.1mm solid #000;
    }

    .ques-box-container .score-table {
      border: none;
    }

    .score-table td {
      border-left: 0.1mm solid #000;
      border-bottom: 0.1mm solid #000;
      text-align: center;
      width: calc(100% / 22);
      line-height: 6mm;
    }

    .score-table td:first {
      border-left: none;
    }

    .score-table td:last-child {
      border-right: unset;
    }

    * {
      box-sizing: border-box;
    }

    body {
      margin: 0;
      padding: 0;
      background-color: #fff;
      width: 100%;
      height: 100%;
      overflow: auto;
      color: #000;
      line-height: 1.125;

    }
  </style>
  <style>
    .align-left {
      display: block !important;
      text-align: left;
    }

    .align-right {
      display: block !important;
      text-align: right;
    }

    .align-center {
      display: block !important;
      text-align: center;
    }

    .card .ques-box {
      padding: 1mm 0 4mm;
    }

    .ques-box-container {
      position: relative;
    }

    .card .ques-box-container::after {
      content: "";
      pointer-events: none;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
      border: 0.1mm solid #000;
    }

    .card .ques-box .vertical {
      /* display: flex; */
    }

    .subject-box {
      text-align: center;
    }

    /* .subject-box.not-writing .subject-para-p .writing-container {
      margin-left: -2mm;
    }

    .subject-box.not-writing .writing-container {
      margin-left: 0;
    } */

    .subject-box>* {
      text-align: left;
    }

    .card .subject-box {
      padding: unset;
      overflow: hidden;
    }

    .card .subject-box .ques-sort {
      width: auto;
      /* vertical-align: top; */
      font-size: inherit;
      /* line-height: 6mm; */
    }

    .card .subject-box .subject-para-p,
    .q-opt.onlycard .ques-box>p {
      padding: 0 3.1mm;
      /* padding: 0 10px; */
    }

    .subject-para-p u {
      word-break: break-all;
    }

    .card .subject-box .subject-para-p .subject-para-p {
      padding: 0;
    }

    .card .subject-box .subject-para-p [data-index="0"] .subject-para-p:first-child {
      line-height: 7mm;
    }

    .q-opt.onlycard .ques-box[data-index="0"] .subject-para-p:first-child {
      line-height: 7mm;
    }

    .ques-name-wrap {
      /* line-height: 7mm; */
      white-space: pre-line;
    }

    .q-opt.is-objective .ques-item-wrap {
      position: relative;
      /* 这里偏移三个单位保持和客观题对齐 */
      left: 3mm;
    }

    .q-opt.is-objective.layout-a33 .ques-item-wrap {
      left: 0;
    }

    .ques-item-wrap {
      display: inline-block;
      margin: 1mm 0;
      /* vertical-align: bottom; */
    }

    .ques-item-wrap--fill {
      vertical-align: top;
      margin: 0;
    }

    .ques-item-wrap--fill .ques-item {
      font-family: 宋体;
    }

    .ques-item-wrap.vertical {
      display: inline-flex;
      flex-wrap: wrap;
      flex-direction: column;
      max-height: 30mm;
    }

    .ques-item-wrap .choice-item {
      margin-right: 5mm;
      padding-right: 0.2mm;
    }

    .ques-item-wrap .is-splited {
      display: block;
    }

    .ques-box .vertical {
      vertical-align: top;
    }

    .ques-box.dis-flex {
      flex-wrap: wrap;
    }

    .ques-box.vertical {
      flex-direction: row;
    }

    .ques-item {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-end;
      position: relative;
      line-height: 4.7mm;
      font-family: "Times New Roman", Times, serif;
    }

    .english .ques-item {
      line-height: unset;
    }

    .choice-item {
      margin-bottom: 1mm;
      display: inline-block;
    }

    .choice-item,
    .choice-item .ques-item {
      font-family: 'Times New Roman';
    }

    .choice-col {
      /* width: calc(100% - 9mm); */
    }

    .ques-sort {
      display: inline-block;
      text-align: center;
      font-size: 3.2mm;
      /* padding-left: 2mm; */
      /* padding-top: 0.2mm; */
    }

    .ques-sort {
      width: 7mm;
      white-space: nowrap;
    }

    .card .ques-sort.hidden {
      visibility: hidden;
    }

    .ques-score {
      display: inline-block;
    }

    .subject-ques-sort {
      display: block;
    }

    .dotWrapper {
      text-align: left;
      width: 100%;
      padding: 0 3mm;
      line-height: 9mm !important;
      font-size: 3.7mm !important;
      font-family: "Times New Roman", Times, serif !important;
      word-break: break-all;
    }

    .dotWrapper u {
      font-family: Times New Roman;
      line-height: 11.5mm;
      text-decoration-thickness: 0;
    }

    .ques-box .four-line-container {
      text-align: center;
    }

    .four-line-grid {
      display: inline-block;
      position: relative;
      height: var(--lineHeight);
    }

    .four-line-grid .four-line {
      position: absolute;
      width: 100%;
      height: 1px;
    }

    /* 定义每条线的位置 */
    .four-line-grid .four-line:nth-child(1) {
      top: 0;
      border-top: 1px solid #707070;
    }

    .four-line-grid .four-line:nth-child(2) {
      top: 33%;
      border-top: 1px dashed #808080;
    }

    .four-line-grid .four-line:nth-child(3) {
      top: 66%;
      border-top: 1px dashed #808080;
    }

    .four-line-grid .four-line:nth-child(4) {
      top: 100%;
      border-top: 1px solid #707070;
    }

    .tianzi-grid {
      display: inline-block;
      width: var(--lineHeight);
      height: var(--lineHeight);
      position: relative;
      border: 1px solid #707070;
      margin: 1px 1px 0 0;
    }

    .tianzi-grid::before,
    .tianzi-grid::after {
      content: "";
      position: absolute;
    }

    .tianzi-grid::before {
      left: 50%;
      border-left: 1px dashed #808080;
      width: 1px;
      height: 100%;
    }

    .tianzi-grid::after {
      top: 50%;
      transform: translateY(-50%);
      width: 100%;
      height: 1px;
      border-top: 1px dashed #808080;
    }

    .dotDot {
      border-bottom: 1px solid #000;
    }

    .option-item {
      display: inline-block;
      width: 6.4mm;
      font-size: 2.6mm;
      text-align: center;
      white-space: nowrap;
    }

    .underline {
      position: relative;
      vertical-align: bottom;
      margin: 0;
      padding: 0;
      /* min-height: 9.8mm; */
      border-bottom: 0.1mm solid #000;
      display: inline-block;
      overflow: hidden;
      font-size: 3.6mm;
      /* margin-right: 1.5mm; */
    }

    .line-list .underline .unline-writing:focus {
      background-color: #f6f6f6;
    }

    .line-list .underline .unline-writing {
      position: relative;
      /* padding-top: 2mm; */
      /* line-height: 7mm; */
      outline: none;
    }

    .line-list .underline .unline-writing~p {
      display: none;
    }

    .opttion-txt {
      vertical-align: middle;
      padding: 0 0.6mm;
    }

    .writing-line {
      width: 100%;
      padding: 1mm 0;
      margin: 0;
      line-height: 7mm;
    }

    .writing-container {
      display: inline-block;
      word-break: break-all;
      text-align: center;
      width: 100%;
    }

    .writing-container:first-child {
      /* padding-top: 3mm; */
    }

    .writing-container:last-child {
      padding-bottom: 3mm;
    }

    .writing-container:empty {
      padding: 0 !important;
    }

    .writing-cell:last-child {
      border-right: 1px solid #999 !important;
    }

    .writing-cell--mark::before {
      content: "";
    }

    .writing-cell--mark::after {
      content: "▲" attr(aria-colcount) "字";
      position: absolute;
      font-size: 12px;
      zoom: 0.7;
      word-break: keep-all;
      top: 100%;
      left: 0;
      line-height: normal;
    }

    .writing-mark {
      font-size: 12px;
      transform: scale(0.7);
      height: 1mm;
      word-break: keep-all;
      position: absolute;
      bottom: 1px;
      left: -4px;
    }

    .writing-cell {
      margin: 0;
      padding: 0;
      border: 1px solid #999;
      display: inline-block;
      vertical-align: bottom;
      position: relative;
      font-size: 4.2mm;
      border-right: 0px;
    }

    .writing-cell-small {
      width: 7mm;
      height: 7mm;
    }

    .writing-cell-medium {
      width: 8mm;
      height: 8mm;
      text-align: center;
      line-height: 8mm;
    }

    .writing-cell-big {
      width: 9.2mm;
      height: 9.2mm;
    }

    /* .line-list {
      margin-left: 2mm;
    } */

    .page-box.onlycard .score-table {
      border: none;
      border-bottom: 0.1mm solid #000;
    }

    .q-opt.onlycard .dotWrapper p,
    .q-opt.onlycard .line-list p {
      line-height: unset;
    }

    .q-opt.onlycard .ques-box .subject-para-p br {
      display: none;
    }

    .q-opt.onlycard .ques-box .subject-para-p::after {
      content: "↵";
      display: inline-block;
      width: 0;
      visibility: hidden;
    }

    .score-opts-list {
      display: inline-block;
      line-height: normal;
    }

    .q-opt.web .fill-item {
      display: flex;
      flex-direction: row;
    }

    .q-opt .ques-item .fill-item {
      display: flex;
      flex-direction: row;
    }

    .q-opt.hand .score-opts-list {
      /* padding-top: 3mm; */
      vertical-align: top;
    }

    .q-opt .fill-item .ques-sort {
      width: 10mm;
      line-height: normal;
      padding-left: 0;
      text-align: right;
      min-height: 6mm;
    }

    .q-opt .split-line {
      position: relative;
      display: block;
      padding: 0.6mm 0;
      cursor: pointer;
    }

    .q-opt .split-line:after {
      content: "";
      display: block;
      width: 100%;
      height: 0.1mm;
      border-bottom: 0.1mm solid;
    }

    .q-opt .split-line.split-line--subject {
      position: absolute;
      margin: -1mm 0;
      z-index: 100;
      left: 0;
      width: 100%;
      box-sizing: border-box;
      border-left: 0.1mm solid;
      border-right: 0.1mm solid;
      display: none;
    }

    .q-opt .split-line.split-line--subject.hide-block {
      background-color: #fff;
      display: block;
    }

    .q-opt .split-line--linefeed {
      visibility: hidden;
      padding: 0;
      height: 0;
    }

    .q-opt.web .split-line.split-line--subject.hide-block::after {
      visibility: hidden;
    }

    .q-opt.layout-a33 .ques-item-wrap .choice-item {
      margin-right: 0;
    }

    .q-opt.merge-img {
      position: relative;
    }

    .q-opt.merge-img .img-list {
      display: flex;
      justify-content: space-around;
    }

    .q-opt.merge-img .img-list .img-item {
      margin: auto;
      position: relative;
    }

    .ques-item-wrap .choice-item .ques-sort {
      padding-right: 0.3mm;
      text-align: right;
      /* transform: translateY(9%); */
    }

    .q-opt.layout-a33 .ques-item-wrap .choice-item .ques-sort {
      width: 6mm;
    }

    .subject-box span.remove-ques+p.remove-ques {
      display: none;
    }

    .subject-box span.remove-ques+.writing-container {
      padding-top: 0;
    }

    .ques-noanswerarea .ques-box {
      position: relative;
      border: 0.1mm solid #000;
      padding: 1mm;
    }
    .ques-noanswerarea .no-edit{
      text-align: center;
      font-size: 12mm;
      font-family: '微软雅黑, Microsoft YaHei, Verdana, Arial, Helvetica, sans-serif';
      color: rgba(0, 0, 0, 0.2);
      font-weight: bold;
      letter-spacing: 10px;
      white-space: nowrap;
    }

    .option-item--fill {
      visibility: hidden;
    }

    .opttion-txt--judge.iconfont {
      font-size: 2.6mm;
      position: relative;
      top: 0.35mm;
      transform: scale(1.2);
      display: inline-block;
    }

    .remove-node {
      display: none;
    }

    .subject-box.is-wrigting .subject-para-p+p {
      display: none;
    }
  </style>
</head>

<body>
  <div id="app">
    <div id="main" class="preview"></div>
    <div class="water-mark">预览，不可下载使用</div>
  </div>
</body>

<link rel="stylesheet" href="./js/theme-chalk_index.css" />
<script src="./js/jquery.min.js"></script>
<script type="text/javascript" src="./js/art-template-web.js"></script>
<script type="text/javascript" src="./js/qrcode.js"></script>
<script type="text/javascript" src="./js/points.js"></script>
<!-- <script defer type="text/javascript" src="http://example.com/ckeditor/plugins/katex"></script> -->

<!-- <link href="https://cdn.bootcdn.net/ajax/libs/KaTeX/0.12.0/katex.min.css" rel="stylesheet">
<script src="https://cdn.bootcdn.net/ajax/libs/KaTeX/0.12.0/katex.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/KaTeX/0.12.0/contrib/auto-render.min.js"></script> -->
<script defer type="text/javascript" src="https://fs.iclass30.com/aliba/plug/mathjax/MathJax.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Register.StartupHook("TeX Jax Ready", function () {
    var TEX = MathJax.InputJax.TeX;
    TEX.prefilterHooks.Add(function (data) {
        if (!data.display) {
            data.math = '\\displaystyle ' + data.math;
        }
        return data;
    });
});
  MathJax.Hub.Config({
    showProcessingMessages: false,
    messageStyle: "none",
    menuSettings: {
      context: "Browser"
    },
    "HTML-CSS": {
      webFont: "STIX-Web",
      availableFonts: ["STIX-Web"],
      preferredFont: "STIX-Web",
      styles: {".wrs_editor .wrs_tickContainer":{display: "none"},".MathJax_Preview": {visibility: "hidden"},".MathJax": {"text-align": "center","margin": "0em 0.3em","padding":"0em","display":"inline-block","transform":"scale(1)"}},
      undefinedFamily: "'Times New Roman', 'Arial Unicode MS', serif",
      scale:110
    },
    "CommonHTML": {
      webFont: "STIX-Web",
      availableFonts: ["STIX-Web"],
      preferredFont: "STIX-Web",
      styles: {".wrs_editor .wrs_tickContainer":{display: "none"},".MathJax_Preview": {visibility: "hidden"},".MathJax": {"text-align": "center","margin": "0em 0.3em","padding":"0em","display":"inline-block","transform":"scale(1)"}},
      undefinedFamily: "'Times New Roman', 'Arial Unicode MS', serif",
      scale:110,
      matchFontHeight: false
    },
    tex2jax: {
      inlineMath: [['$$','$$'], ['$','$'],["\\(","\\)"],["\\[", "\\]"]],
      processEscapes: true,
      skipTags: ['script', 'style',  'pre', 'code']
    },
    MMLorHTML: {prefer: "HTML"},
    jax: ["input/TeX","output/CommonHTML"],
    extensions: ["tex2jax.js","mml2jax.js","MathMenu.js","MathZoom.js", "fast-preview.js", "AssistiveMML.js"],
    TeX: {
      extensions: ["AMSmath.js","AMSsymbols.js","noErrors.js","noUndefined.js","cancel.js"]
    },
    font:"STIX-Web",

    SVG: {
      scale: 110,
      linebreaks: {
           automatic: true
      },
      addMMLclasses: false
    }
  });
  </script>
<!-- <script type="text/javascript" id="MathJax-script"
  src="https://fs.iclass30.com/package/mathjax@3.2.0/es5/tex-svg-full.js"></script> -->
<script id="ques-page" type="text/html">
    <% for(var page = 1; page <= pageCount; page++){ %>
    <div class="preview-warp">
      <!-- 拍改模式不显示采样点 -->
      <% if(correctType != 3 && !isSealLine){ %>
      <div class="preview-points">
        <div class="left-point left-point1 side-point"></div>
        <div class="left-point left-point2 side-point"></div>
        <div class="left-point left-point3 side-point"></div>
        <div class="right-point right-point1 side-point"></div>
        <div class="right-point right-point2 side-point"></div>
        <div class="right-point right-point3 side-point"></div>
      </div>
      <% } %>

      <!-- 拍改模式不显示页眉 -->
      <% if(correctType !=3 ) { %>
      <div class="preview-header" style="height: 13mm;">
        <div class="locatePoint-l"></div>
        <div class="locatePoint-r"></div>
        <span class="styles_left styles_idAndNameBase">
          编号：{{@paperId}}
        </span>
        <span
          class="custom-name"
          style="left: 0;text-align: center;right: 0;position: absolute;bottom: 1mm;font-size: 3.2mm;font-family: 宋体;"
        >
          {{ customName }}
        </span>
      </div>
      <% } %>

      <div style="height: 1mm;width: 100%;"></div>
      <div class="preview-content">
        <% for(var sf = 1; sf <= step; sf++){ %> <% if(hasSurface(sf,page)){ %>
        <div class="surface-warp" style="width:130mm !important;"></div>
        <%} else if(hasWidthSurface(sf,page)){%>
        <div class="surface-warp" style="width:192mm !important;"></div>
        <%}%> <% if(hasLine(sf,page)){ %>
        <div class="line" style="width:4mm !important;"></div>
        <%} else if(hasBoldLine(sf,page)){%> <% if(step == 3){ %>
        <div class="line" style="width:14mm !important;"></div>
        <% }else{ %>
        <div class="line" style="width:8mm !important;"></div>
        <% } %> <%}%> <%}%>
      </div>

      <!-- 拍改模式不显示页脚 -->
      <% if(correctType !=3 ) { %>
      <div class="preview-footer">
        <div class="locatePoint-l"></div>
        <div class="locatePoint-l-new" id="locatePoint-l-new"></div>
        <div class="locatePoint-r"></div>
        <div class="locatePoint-r-new" id="locatePoint-r-new"></div>
        <span class="page">第{{@page}}页</span
        ><span class="total">(共{{@pageCount}}页)</span>
      </div>
      <% } %>

      <% if(page % 2 == 0 && isSealLine){ %>
      <div class="hand-write-div right" style="position: absolute; top: 14mm;">
        <div
          style="top:0mm;right:12mm;width:10mm;height:269mm;border-right:1px dashed #000;position:absolute;z-index:0;"
        ></div>
        <span
          style="width:30px;letter-spacing:0px;display:block;position:absolute;right:8mm;transform:rotate(90deg);white-space:nowrap;"
          ><span style="padding:0 30mm;"></span>密<span
            style="padding:0 30mm;"
          ></span
          >封<span style="padding:0 30mm;"></span>线<span
            style="padding:0 30mm;"
          ></span>
        </span>
      </div>
      <% } %> <% if(page % 2 != 0 && isSealLine){ %>
      <div class="hand-write-div left" style="position: absolute; top: 14mm;">
        <div
          style="top:0mm;left:2mm;width:10mm;height:269mm;border-right:1px dashed #000;position:absolute;z-index:0;"
        ></div>
        <span
          style="width:30px;letter-spacing:0px;display:block;position:absolute;left:8mm;top:250mm;transform:rotate(-90deg);white-space:nowrap;"
          ><span style="padding:0 30mm;"></span>密<span
            style="padding:0 30mm;"
          ></span
          >封<span style="padding:0 30mm;"></span>线<span
            style="padding:0 30mm;"
          ></span
        ></span>
        <div
          style="z-index: 1; top: 0mm; left: 1mm; width: 10mm; height: 269mm; position: absolute; font-size: 3.7mm;"
        >
          <table
            cellpadding="0"
            cellspacing="0"
            style="table-layout: fixed; border-collapse: collapse; width: 100%; height: 90%; margin-top: 20mm;"
          >
            <tbody>
              <tr>
                <td
                  style="border: none; word-break: keep-all; font-weight: bold;"
                >
                <% if(!isWrite){ %>
                  <span
                    style="display: block; height: 30mm; border-left: 1px solid rgb(51, 51, 51); margin-left: 32px; margin-bottom: 10px;"
                  ></span
                  >
                <% } else { %>
                  <table id="stu-no-three-tr" v-else style="display: inline-block;margin-left: 3mm;">
                    <% for(var no = 1; no <= stuNoLength; no++){ %> 
                    <tr>
                      <td style="width: 6mm;height: 6mm;border: 1px solid;border-right: 1px solid;border-left: 1px solid;border-bottom: <%= no == stuNoLength ? '1px solid' : '1px dashed' %>;border-top: <%= no == 1 ? '1px solid' : '1px dashed' %>;" class="write-box">
                      </td>
                    </tr>
                    <% } %>
                </table>
                <% } %>
                  <span
                    style="display: block; transform: rotate(-90deg); white-space: nowrap; width: 50px;"
                    >考号：</span
                  >
                </td>
              </tr>
              <tr>
                <td
                  style="border: none; word-break: keep-all; font-weight: bold;" id="stu-name"
                >
                  <span
                    style="display: block; height: 30mm; border-left: 1px solid rgb(51, 51, 51); margin-left: 32px; margin-bottom: 10px;"
                  ></span
                  ><span
                    style="display: block; transform: rotate(-90deg); white-space: nowrap; width: 50px;"
                    >姓名：</span
                  >
                </td>
              </tr>
              <tr>
                <td
                  style="border: none; word-break: keep-all; font-weight: bold;"
                >
                  <span
                    style="display: block; height: 30mm; border-left: 1px solid rgb(51, 51, 51); margin-left: 32px; margin-bottom: 100px;"
                  ></span
                  ><span
                    style="display: inline-block; transform: rotate(-90deg); white-space: nowrap; width: 50px;"
                    >&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;班级：</span
                  >
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <% } %>
    </div>
    <%}%>
  </script>
<script type="text/javascript">
  $(function () {
    const hostList = {
      test: {
        url: "https://test.iclass30.com",
        kklUrl: "https://kklservicetest.iclass30.com",
      },
      prod: {
        url: "https://service.iclass30.com",
        kklUrl: "https://kklservice.iclass30.com",
      },
    };
    // 可以根据指定参数或根据域名判定现测网环境
    const env = getQueryString("env") || "prod";
    const v = getQueryString("pv") || "1";

    const url = getQueryString("url") || hostList[env].url;
    const kklUrl = hostList[env].kklUrl;
    const id = getQueryString("id") || "1000380";
    const isPreview = getQueryString("preview") == "1";
    const isSend = getQueryString("isSend") == "1";
    const IPAGELAYOUT = {
      A4: 1,
      A3: 2,
      A33: 3,
      A32: 4,
      A23: 5,
    };
    let step = 1;
    let paperColCount = 1;
    getUrl(url, kklUrl);
    function getPaperInfo() {
      saveLog(getCurrentTime() + "调用getViewPaper");
      $.ajax({
        type: "POST",
        url: url + "/testbank/testBank/getViewPaper",
        data: {
          paperNo: id,
        },
        dataType: "json",
        success: function (res) {
          if (res.code === 1) {
            saveLog(getCurrentTime() + "调用getViewPaper成功");
            let cardInfo = JSON.parse(res.data.cardInfo);
            let quesInfo = JSON.parse(res.data.quesInfo);
            let subjectid = res.data.subjectId;
            console.log(cardInfo);
            console.log(quesInfo);
            //document.body.style.zoom = cardInfo.zoom / 100;
            let dom = new DOMParser().parseFromString(
              cardInfo.html,
              "text/html"
            );
            let surfaceCount =
              dom.getElementsByClassName("header-box").length;
            step =
              cardInfo.pageLayout === IPAGELAYOUT.A4
                ? 1
                : cardInfo.pageLayout === IPAGELAYOUT.A3
                  ? 2
                  : 3;
            // 每张纸的分栏数量
            paperColCount = [IPAGELAYOUT.A23, IPAGELAYOUT.A32].includes(
              Number(cardInfo.pageLayout)
            )
              ? 5
              : step * 2;
            let pageCount = Math.ceil(surfaceCount / step);
            if (cardInfo.pageLayout === IPAGELAYOUT.A4) {
              document.getElementById("app").style.width = "210mm";
            } else {
              document.getElementById("app").style.width = "420mm";
            }

            if (cardInfo.cardType != 1 && cardInfo.cardType != 4) {
              $("#main").addClass("card");
            }
            if (cardInfo.cardType == 1) {
              $("#main").addClass("quescard");
            }
            if (cardInfo.cardType == 4) {
              $("#main").addClass("english");
            }
            var styleEl = document.createElement("style");
            styleEl.innerHTML = `#main{ --fontSize: ${cardInfo.fontSize};--fontFamily:${cardInfo.fontFamily};--lineHeight:${cardInfo.space} }`;
            document.head.appendChild(styleEl);
            $("#main,#app").addClass(
              cardInfo.pageLayout === IPAGELAYOUT.A4
                ? "a4"
                : cardInfo.pageLayout === IPAGELAYOUT.A3
                  ? "a3"
                  : "a33"
            );
            buildTplFun(cardInfo);
            $("#main")
              .empty()
              .append(
                template("ques-page", {
                  pageCount: pageCount,
                  step: step,
                  paperId: id,
                  correctType: cardInfo.correctType,
                  customName: cardInfo.headerName,
                  isSealLine:
                    getQueryString("hand") == "1" || cardInfo.isSealLine,
                  isWrite: cardInfo.numberLayout == 3,
                  stuNoLength: res.data.stuNoLength
                })
              );
            copyNode(dom);
            document.querySelectorAll("[id^=qrcode]").forEach((dom) => {
              new QRCode(dom.id, {
                text: id + dom.getAttribute("page").padStart(2, "0") + "," + v,
                width: 75.6,
                height: 75.6,
                correctLevel: QRCode.CorrectLevel.L,
              });
            });
            function executeWithDelay() {
              //判断图片是否全部加载完成
              let isLoadImg = Array.from(document.querySelectorAll('img')).find(img => { return !img.complete });
              if (isLoadImg) {
                saveLog(getCurrentTime() + "图片加载未完成，延迟1s后继续尝试执行");
                setTimeout(() => {
                  executeWithDelay();
                }, 1000);
                return;
              }
              setTimeout(function () {
                // 拍改模式不生成坐标点
                if (isSend && cardInfo.correctType != 3) {
                  compute(quesInfo, cardInfo,pageCount,res.data.abCardType);
                } else {
                  completePrintPdf();
                }
              }, 500);
            }
            if ([25, 3, 12].includes(Number(subjectid))) {
              executeWithDelay()
            } else {
              MathJax.Hub.Queue(["Typeset", MathJax.Hub], function () {
                executeWithDelay()
              });
            }
          }
        },
      });
    }
    if (isPreview) {
      $(".water-mark").show();
    }
    getPaperInfo();

    const buildTplFun = (cardInfo) => {
      template.defaults.imports.hasSurface = (index, page) => {
        if (cardInfo.pageLayout == IPAGELAYOUT.A33) {
          return true;
        }
        if (
          cardInfo.pageLayout == IPAGELAYOUT.A3 ||
          cardInfo.pageLayout == IPAGELAYOUT.A4
        ) {
          return false;
        }
        const layoutConfigs = {
          [IPAGELAYOUT.A32]: {
            first: [1, 2, 3],
            last: [],
          },
          [IPAGELAYOUT.A23]: {
            first: [],
            last: [1, 2, 3],
          },
        };
        const { first, last } = layoutConfigs[cardInfo.pageLayout];
        return (page % 2 === 0 ? last : first).includes(index);
      };
      template.defaults.imports.hasWidthSurface = (index, page) => {
        if (cardInfo.pageLayout == IPAGELAYOUT.A33) {
          return false;
        }
        if (
          cardInfo.pageLayout == IPAGELAYOUT.A4 ||
          cardInfo.pageLayout == IPAGELAYOUT.A3
        ) {
          return true;
        }
        const layoutConfigs = {
          [IPAGELAYOUT.A32]: {
            first: [],
            last: [1, 2],
          },
          [IPAGELAYOUT.A23]: {
            first: [1, 2],
            last: [],
          },
        };
        const { first, last } = layoutConfigs[cardInfo.pageLayout];
        return (page % 2 === 0 ? last : first).includes(index);
      };
      template.defaults.imports.hasLine = (index, page) => {
        const layouts = {
          [IPAGELAYOUT.A33]: [1, 2],
          [IPAGELAYOUT.A32]: page % 2 ? [1, 2] : [],
          [IPAGELAYOUT.A23]: page % 2 ? [] : [1, 2],
        };
        const targetLayout = layouts[cardInfo.pageLayout];
        return targetLayout && targetLayout.includes(index);
      };
      template.defaults.imports.hasBoldLine = (index, page) => {
        const layouts = {
          [IPAGELAYOUT.A3]: [1],
          [IPAGELAYOUT.A32]: page % 2 ? [] : [1],
          [IPAGELAYOUT.A23]: page % 2 ? [1] : [],
        };
        const targetLayout = layouts[cardInfo.pageLayout];
        return targetLayout && targetLayout.includes(index);
      };
    };

    // 判断两个数据是否有交集
    const hasIntersection = (arr1, arr2) => arr1.some(Set.prototype.has, new Set(arr2));
    const copyNode = (el) => {
      const children = el.body.children;
      let headerCount = 1;
      let index = -1;
      for (let i = 0; i < children.length; i++) {
        children[i].style.width = "";
        // 计数splitor节点来判断换页面
        let surfaceWarps = document.getElementsByClassName("surface-warp");
        let $surfaceWarp = null;
        let isSplitor =
          (children[i].classList.contains("header-box") &&
            !children[i].classList.contains("absolute")) ||
          children[i].classList.contains("splitor-empty");

        if (isSplitor) {
          index++;
          $surfaceWarp = surfaceWarps[index];
          if (!$surfaceWarp) continue;
          $surfaceWarp.innerHTML = "";
        }

        $surfaceWarp = surfaceWarps[index];
        if (!$surfaceWarp) continue;

        if (
          hasIntersection(
            ['header-box', 'splitor-empty', 'footer', 'page-line'],
            children[i].classList
          )
        )
          continue;

        let quesCard = children[i].cloneNode(true);
        let isFillCard = quesCard.className.includes('card--fill');
        let fiilImages = [];
        if (isFillCard) {
          // 填空题判断图片位置,拆分出不同组的图片
          let $imglistContainer = quesCard.querySelectorAll('.imglist-content');
          if ($imglistContainer) {
            let $imgsContainer = $imglistContainer[0];

            if ($imgsContainer.childElementCount) {
              Array.from($imgsContainer.children).forEach($img => {
                fiilImages.push($img.cloneNode(true));
              });
            }
          }
        }

        if (quesCard.className.includes("q-opt")) {
          let splitors = Array.from(
            quesCard.querySelectorAll(".page-splitor")
          );
          if (splitors.length) {
            let $parent = splitors[0].parentElement;
            let childLength = $parent.children.length;
            let $score = Array.from($parent.children).find((item) =>
              item.className.includes("score-container")
            );
            let splitChildren = Array.from($parent.children);

            splitChildren.forEach(($el, elIndex) => {
              let isScoreNameEl =
                $el.className.includes("score-container") ||
                $el.className.includes("ques-content-name");
              if ($el.className.includes("page-splitor") || isScoreNameEl) {
                // 不参与计算的元素减少总数量
                if (isScoreNameEl) childLength--;
                return;
              }

              let quesCardClone = children[i].cloneNode(true);
              let splitors = Array.from(
                quesCardClone.querySelectorAll(".page-splitor")
              );
              let $parentClone = splitors[0].parentElement;
              $parentClone.innerHTML = "";

              // 检测是否需要插入分数
              if ($score) {
                let isAfter = $score.className.includes("after");
                if (isAfter ? elIndex >= childLength : elIndex === 1) {
                  $parentClone.appendChild($score);
                }
              }

              $parentClone.appendChild($el);
              $surfaceWarp.appendChild(quesCardClone);

              // 填空题分摊图片到不同的分段
              if (isFillCard) {
                let $imglistContainer = quesCardClone.querySelectorAll('.imglist-content');
                if ($imglistContainer) {
                  let $imgsContainer = $imglistContainer[0];
                  $imgsContainer.innerHTML = '';
                }
                if (fiilImages.length) {
                  let elHeight = $el.offsetHeight;
                  if ($imglistContainer) {
                    let $imgsContainer = $imglistContainer[0];
                    fiilImages = fiilImages.filter(img => {
                      let imgH = img.offsetHeight;
                      let imgTop = Number(img.style.top.replace('px', ''));

                      if (imgTop < elHeight - imgH / 2) {
                        $imgsContainer.appendChild(img);
                        return false;
                      } else {
                        let $names = quesCardClone.querySelectorAll('.ques-content-name');
                        imgTop = imgTop - elHeight - mmConversionPx(28);
                        if (elIndex === 0 && $names.length) {
                          img.style.top = imgTop - $names[0].offsetHeight + 'px';
                        } else {
                          img.style.top = imgTop + 'px';
                        }
                        return true;
                      }
                    });
                  }
                }
              }

              let spIndex = Number($el.dataset.index || elIndex);
              // 只保留最后一个拆分线
              let splitLines = quesCardClone.querySelectorAll('.split-line--subject');
              if (splitLines.length && elIndex <= $parent.children.length) {
                splitLines[0].remove();
              }
              if (spIndex > 0) {
                // 清除第一个分段之外的标题
                let $nameEle =
                  quesCardClone.querySelectorAll(".ques-content-name");
                if (!$nameEle.length) {
                  $nameEle = quesCardClone.querySelectorAll(
                    ".ques-content-name--small"
                  );
                }
                if ($nameEle.length) $nameEle[0].remove();
              }

              if (elIndex < childLength - 1) {
                index++;
                $surfaceWarp = surfaceWarps[index];
              }
            });

            continue;
          }
        }

        $surfaceWarp.appendChild(quesCard);

        if (quesCard.className.includes("header-info absolute")) {
          // 新纸张的头部为绝对定位，这里需要将位置调换到前排
          $surfaceWarp = surfaceWarps[paperColCount * headerCount];
          let firstChild = $surfaceWarp.firstElementChild;
          if (firstChild) {
            $surfaceWarp.insertBefore(quesCard, firstChild);
          } else {
            $surfaceWarp.appendChild(quesCard);
          }
          headerCount++;
        }
      }
    };

    function getQueryString(name) {
      let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
      let r = window.location.search.substr(1).match(reg);
      if (r != null) return decodeURIComponent(r[2]);
      return null;
    }
  });
</script>

</html>