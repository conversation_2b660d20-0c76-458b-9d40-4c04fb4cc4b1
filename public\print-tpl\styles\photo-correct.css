.q-opt.photo-correct-area {
    display: block;
    width: auto;
    min-width: 80mm;
    margin: 10mm auto 0;
    font-family: "Times New Roman";
    border: 3px solid #000;
    border-radius: 1px;
}

.q-opt.photo-correct-area .br-block {
    float: none;
    clear: both;
    height: 2mm;
}

.photo-correct-area .stu-no-table {
    float: right;
    border-collapse: separate;
    height: 287px;
    border-spacing: 4px;
    margin-left: 10px;
}

.photo-correct-area .stu-num-full-sec {
    width: 100%;
    height: 4.7mm;
    color: #666;
    border: 0.4mm solid #000 !important;
}


.photo-correct-area .stu-no-table .table-caption th {
    padding: 0 5px;
    font-size: 12px;
}

.photo-correct-area .ques-sort {
    font-size: 10px;
    width: 7mm;
    text-align: right;
    padding-right: 3px;
}

.photo-correct-area .option-item {
    font-family: "Times New Roman";
}

.choice-item--rect {
    margin: 2.2mm 0.2mm 0;
}

.choice-item--rect .option-item {
    width: 6mm;
    margin: 0 0.8mm;
    line-height: 4mm;
    height: 4.7mm;
    border: 0.4mm solid #000;
    font-family: "Times New Roman";
    color: #666;
}

.stu-no-table--rect th {
    border: 0.4mm solid #000 !important;
}

.stu-no-table--rect td {
    border: none !important;
}

.stu-no-table--rect .stu-no-second-tr {
    height: 6mm;
}

.stu-no-table--rect .stu-num-td {
    width: 6mm;
}

.stu-no-table--rect .stu-num-txt {
    font-family: Arial, 'Microsoft YaHei';
}

.photo-correct-area .stu-bar-code-container {
    width: 65mm;
    height: 30mm;
    padding: 5mm;
    border-left: 1px solid #000;
    border-bottom: 1px solid #000;
    margin-left: 2mm;
    margin-bottom: 4.5mm;
}