const zoom = window.PRINT_ZOOM;
const IPAGELAYOUT = {
  A4: 1,
  A3: 2,
  a33: 3,
};

// const url = getQueryString("url") || "https://test.iclass30.com";
// const kklUrl = "https://kklservicetest.iclass30.com";
const isSend = getQueryString("isSend") == "1";
const id = getQueryString("id") || "1000380";
const v = getQueryString("pv") || "1";
let url = "";
let kklUrl = "";
let chooseQuesNo = 10000;
function getUrl(newUrl, newKklUrl) {
  [url, kklUrl] = [newUrl, newKklUrl];
}

function getQueryString(name) {
  let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  let r = window.location.search.substr(1).match(reg);
  if (r != null) return decodeURIComponent(r[2]);
  return null;
}
function isElementVisible(ele){
  try {
    const style = window.getComputedStyle(ele);
    const visibility = style.getPropertyValue("visibility");
    if(visibility){
      return visibility === "visible"
    }else{
      return ele?.checkVisibility()
    }
  } catch (e) {
    //兼容低版本不支持checkVisibility
    return ele && getWidth(ele) > 0 && getHeight(ele) > 0;
  }
}

function getCurrentTime() {
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth() + 1;
  const day = currentDate.getDate();
  const hours = currentDate.getHours();
  const minutes = currentDate.getMinutes();
  const seconds = currentDate.getSeconds();
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
function isChromeVersionGreaterThan127() {
  const userAgent = navigator.userAgent;

  // 查找 "Chrome/XX" 或 "Chromium/XX" 版本号
  const chromeMatch = userAgent.match(/Chrome\/(\d+)/) || userAgent.match(/Chromium\/(\d+)/);

  if (chromeMatch && chromeMatch[1]) {
    const version = parseInt(chromeMatch[1], 10);
    return version > 127;
  }

  // 如果不是 Chrome 或 Chromium，返回 false
  return false;
}

/**
 * @name:计算最终坐标数据
 * @param {*} quesInfo 题目结构
 * @param {*} cardInfo 题卡信息
 * @param {*} pageCount 页数
 * @param {*} abCardType AB卷类型 0:普通卡 1:ab卡 2:AB卷
 */
const compute = (quesInfo, cardInfo,pageCount,abCardType) => {
  let $app = document.getElementById('app')
  if(!isChromeVersionGreaterThan127()){
    $app.style.zoom = zoom;
  }

  saveLog(getCurrentTime() + ">>" + id + ">>进计算坐标点");
  let pointData = [];
  //定位点
  const markPoint = computePoints(cardInfo.pageLayout);
  //辅助定位点
  const helpLocatePoint = computeLocatePoint();
  //考号位置
  const stuNo = computeStuNo(cardInfo.numberLayout);
  //二维码位置
  const qrPoint = computeQrcode();
  //注意事项位置
  const noticePoint = computeNoticeInfo(
    cardInfo.pageLayout,
    cardInfo.numberLayout
  );
  //缺考标记
  const missExamMark = computeMissExamMark();
  //ab标记
  const abMark = computeABMark();
  //姓名位置
  const currentDom = document.getElementById("stu-name");
  let stuNamePoints = "";
  if (currentDom) {
    stuNamePoints = computestuName();
  }
  //班级位置
  const classDom = document.getElementById("class-name");
  let clsNamePoints = "";
  if (classDom) {
    clsNamePoints = computeClsName();
  }
  quesInfo.forEach((item) => {
    let points = null;
    //选做题
    if(item.doQuesList && item.doQuesList.length){
      {
        points = computeChooseDo(item,cardInfo.cardType,)
        let page = computePage(points);
        pointData.push(page);
      }
    }
    if (cardInfo.cardType == 0) {
      // 纯答题卡
      const items = (item.mixinMode || item.data.length) ? item.data : [item];
      items.forEach((ite) => {
        if (!ite.data) {
          ite.data = [JSON.parse(JSON.stringify(ite))];
        }
        points = computeCoordinate(
          cardInfo.ansToTopConfig,
          cardInfo.cardType,
          ite,
          item,
          cardInfo.correctType,
          cardInfo.ansType
        );
        let page = computePage(points);
        pointData.push(page);
      });
    } else {
      const dataInfo = item.data[0].data ? item.data[0] : item;
      points = computeCoordinate(
        cardInfo.ansToTopConfig,
        cardInfo.cardType,
        dataInfo,
        item,
        cardInfo.correctType,
        cardInfo.ansType
      );
      let page = computePage(points);
      pointData.push(page);
    }
  });
  let singleQues = [];
  pointData.forEach((item) => {
    item.forEach((ite) => {
      singleQues.push(ite);
    });
  });
  // 合并页后的数据
  let pageData = groupValuesByKey(singleQues);
  const isA4Layout = cardInfo.pageLayout == 1;
  // 获取总页数
  let pageSize = Array.from({ length: pageCount }, (_, i) => i + 1);
  //组装最终需要的数据
  const finalData = {
    width: 210,
    height: 297,
    type: cardInfo.correctType,
    page_num: pageSize.length,
    margin: {
      num: 4,
      pos: markPoint,
      width: 7,
      height: 3,
      square: 4,
    },
    pages: [],
  };
  pageSize.forEach((item, index) => {
    const pages = [...pageData[item]||[]];
    //插入考号、二维码、注意事项、姓名位置
    if (item % 2 == 1) {
      if (clsNamePoints != "" && clsNamePoints.pos.length) {
        pages.unshift(clsNamePoints);
      }
      if (stuNamePoints != "" && stuNamePoints.pos.length) {
        pages.unshift(stuNamePoints);
      }
      if(cardInfo.numberLayout != 4 && cardInfo.numberLayout != 5){
        if(abCardType == 2){
          pages.unshift(abMark)
        }
        pages.unshift(qrPoint, stuNo, noticePoint, missExamMark);
      }else{
        //极简二维码不包含缺考 注意事项
        pages.unshift(qrPoint, stuNo);
      }
    }
    pages.unshift(helpLocatePoint);
    finalData.pages.push(pages);
  });
  let tempPages = []; // 存储合并后的对象的数组
  for (let i = 0; i < finalData.pages.length; i++) {
    tempPages.push(finalData.pages[i]);
  }
  if (!isA4Layout) {
    finalData.pages = tempPages;
    finalData.page_num = finalData.pages.length;
    finalData.width = 420;
  }
  const data = restoreRatio(finalData);
  console.log("points>>>", data);
  cardInfo.points = data;
  saveLog(getCurrentTime() + ">>" + id + ">>完成坐标点计算");
  // saveLog(getCurrentTime() + ">>" + "坐标点" + JSON.stringify(data));
  savePoints(cardInfo, finalData.page_num);
};

/**
 * @description: 打印pdf
 * @return {*}
 */
const completePrintPdf = () => {
  let $app = document.getElementById('app')
  try {
    $app.style.zoom = zoom;
    $app.classList.add('card-zoom');

    window.BuildPdf(1 / zoom);
  } catch (error) {
    $app.style.zoom = 1;
    console.error(error);
  }
}

/**
 * @name:保存坐标数据
 */
function savePoints(cardInfo, paperNum) {
  saveLog(getCurrentTime() + ">>" + id + ">>更新updateBigViewPaper");
  const params = {
    paperNo: id,
    cardInfo: cardInfo,
    paperNum: paperNum || 1,
    updateTeamInfo: 1,
    source: 1,
    pv: v
  };

  // 使用 Promise 包装 jQuery ajax
  return new Promise((resolve, reject) => {
    $.ajax({
      type: "POST",
      url: url + "/testbank/testBank/updateBigViewPaper",
      headers: {
        "Content-Type": "application/json",
      },
      data: JSON.stringify({
        json: JSON.stringify(params)
      }),
      success: resolve,
      error: reject
    });
  })
  .then(res => {
    if (res.code == 1) {
      saveLog(getCurrentTime() + ">>" + id + ">>更新updateBigViewPaper成功");
      setTimeout(() => {
        saveLog(getCurrentTime() + ">>" + id + ">>调BuildPdf");
        completePrintPdf();
      }, 500);
    } else {
      saveLog(getCurrentTime() + ">>" + id + ">>更新updateBigViewPaper失败");
    }
  })
  .catch(error => {
    saveLog(getCurrentTime() + ">>" + id + ">>更新updateBigViewPaper失败: " + error.message);
    console.error('请求失败:', error);
  });
}

/**
 * @name:日志
 */
function saveLog(message) {
  const params = new URLSearchParams({
    msg: JSON.stringify(message)
  });

  fetch(`${kklUrl}/pexam/_/print/log?${params}`, {
    method: 'GET',
  })
  .then(response => response.json())
  .then(res => {
    if (res.code == 1) {
      console.log(message);
    }
  })
  .catch(error => {
    console.error('日志记录失败:', error);
  });
}

/**
 * @name:计算题目坐标位置
 * @param {*} ansToTop 作答前置  0：否 1：是
 * @param {*} cardType 题卡类型 0纯答题卡 1题卡合一 2题卡分离
 * @param {*} quesInfo 题目结构数据
 * @param {*} bigQuesInfo 大题数据
 * @param {*} correctType 1:手阅 2:网阅
 * @returns
 */
function computeCoordinate(ansToTopConfig, cardType, quesInfo, bigQuesInfo,correctType,ansType) {
  let optionData = [];
  //原点dom
  const initDom = "app";
  quesInfo.data.forEach((item, index) => {
    let scoreType = 4; //打分类型，3 :填涂，4:划线 13:手写分值  15：手写对错
    let quesType = item.typeId;
    //打分模式 1:分数格  2:对错  3:仅识别错误  4：手写打分   5：填空题线上批改
    let markType = item.markType || quesInfo.markType || bigQuesInfo.markType;
    // 十个位分开 1:是  2:否
    let gridType = item.gridType || quesInfo.gridType || bigQuesInfo.gridType;
    //是否智批
    let isAIscan = item.scanMode == 1;
    let quesName = item.quesNos;
    if(cardType != 1 && item.isChooseDo){
      quesName = `${item.chooseName.join(',')}(${item.chooseIds.length}选${item.targetIds.length})`;
    }
    //手写打分
    if(markType == 4){
      scoreType = 13;
    }
    //线上手写对错
    if(markType == 5){
      scoreType = 15;
    }
    //单选，多选，判断题
    if (quesType == 8 || quesType == 1 || quesType == 2) {
      if(ansType == 2 && quesType != 2){
        //手写作答
        scoreType = 16;
      }else{
        scoreType = 3; //填涂题
      }
    } else if (quesType == 7) {
      scoreType = 7; // 智能批改
    }
    let tempDom = "";
    if (cardType == 1) {
      //题卡合一
      //作答前置(填空、选择)
      if (
        (ansToTopConfig.object && [1, 2, 8].includes(Number(quesType)))
        || (ansToTopConfig.fill && [3,7].includes(Number(quesType)))
      ) {
        tempDom = document.querySelectorAll(`[qid="${item.id}"]`);
      } else {
        tempDom = document.querySelectorAll(`[id="${item.id}"]`);
      }
    } else {
      // 纯答题卡
      tempDom = document.querySelectorAll(`[qid="${item.id}"]`);
    }
    //坐标数据
    let pointData = {};
    //填涂题
    if (scoreType == 3 || scoreType == 16) {
      for (let i = 0; i < tempDom.length; i++) {
        //选择选项或打分格
        let optionsItem = [];
        //找出正确答案下标
        let correctIndex = [];
        //每个选项坐标
        let rightOption = [];
        optionsItem = tempDom[i].querySelectorAll(
          cardType == 1 && !ansToTopConfig.object ? ".ocr-pos" : `.option-item`
        );
        if(scoreType == 16){
          optionsItem = tempDom[i].querySelectorAll(".opts-write");
        }
        //兼容每题选择题选项个数不一致的情况
        if (
          !(cardType == 1 && !ansToTopConfig.object) &&
          (quesType == 8 || quesType == 1)
        ) {
          let optionCount = item.optionCount
            ? item.optionCount
            : quesInfo.optionCount;
          optionsItem = Array.from(optionsItem).splice(0, optionCount);
        }

        if (optionsItem.length > 0) {
          //正确答案标记
          let rightClass =
            cardType == 1 && !ansToTopConfig.object ? "isAns" : "full-color";
          optionsItem.forEach((ite, ind) => {
            if(isElementVisible(ite)){
              let id = `${item.id}_${i}_${ind}`;
              ite.setAttribute("id", id);
              const box = getPoints(id, "app");
              rightOption.push(box);
              if (optionsItem[ind].classList.contains(rightClass)) {
                // 正确答案下标
                correctIndex.push(ind);
              }
            }
          });
        }
        pointData = {
          // 填涂题
          item_type: scoreType,
          pos: getSplitPoints(tempDom[i], initDom),
          // 题号 同一题可以跨页，但题号question_no要一致，最好不要跨页
          question_no: Number(item.quesNo),
          question_id: item.id,
          // 题目字符串显示，可以不要，给前端显示的
          question_nos: quesName,
          //题型
          question_type: Number(quesType),
          // 多选
          multi: quesType == 1,
          option_len: item.optionCount,
          // 正确答案下标
          answers: correctIndex,
          // 满分
          total_score: Number(item.score),
          // 漏选分数
          miss_score:
            quesType == 1
              ? bigQuesInfo.halfScore
                ? bigQuesInfo.halfScore
                : bigQuesInfo.halfScore == 0
                  ? 0
                  : bigQuesInfo.quesScore / 2
              : 0,
          // 每个���项的位置
          option_list: rightOption,
        };
        optionData.push(pointData);
      }
    } else if(scoreType == 13){
      // 漏选分值
      let missScore = -1;
      for (let i = 0; i < tempDom.length; i++) {
        //打分格
        let optionsItem = tempDom[i].querySelectorAll(".write-score-item");
        let scoresList = [];
        // 简答题题目区域高
        let subQuesScoreBox = [];
        subQuesScoreBox = getSplitPoints(tempDom[i], "app");
        for (let j = 0; j < optionsItem.length; j++) {
          const scoreEl = optionsItem[j];
          let id = `${item.id}_${j}_mark`;
          scoreEl.setAttribute("id", id);
          let scoreBox = getPoints(id, "app");
          scoresList.push(
            {
              pos: scoreBox,
            },
          );
        }
        if (![1, 2, 8].includes(Number(quesType))) {
          //主观题区域高度增加2mm
          subQuesScoreBox[3] = subQuesScoreBox[3] + 2;
        }
        if ([3,7].includes(Number(quesType)) && (cardType == 0 || cardType == 2 || cardType == 4 || ansToTopConfig.fill)) {
          //纯卡填空题去除序号宽度
          let width = 10;
          if(cardType == 4){
            width = 25;
          }
          subQuesScoreBox[0] = subQuesScoreBox[0] + width;
          subQuesScoreBox[2] = subQuesScoreBox[2] - width;
        }
        pointData = {
          // 划线打分题
          item_type: scoreType,
          //题目坐标
          pos: [
            subQuesScoreBox[0],
            subQuesScoreBox[1],
            subQuesScoreBox[2],
            subQuesScoreBox[3],
          ],
          question_no: Number(item.quesNo),
          question_id: item.id,
          // 可以是5-10合并题目给分
          question_nos: quesName,
          //题型
          question_type: Number(quesType),
          total_score: Number(item.score),
          miss_score: missScore,
          score_list: scoresList,
          word: item.answer,
        };
        optionData.push(pointData);
      }
    } else {
      // 漏选分值
      let missScore = -1;
      for (let i = 0; i < tempDom.length; i++) {
        //打分格
        let optionsItem = [];
        let scoresList = [];
        // 简答题题目区域高
        let subQuesScoreBox = [];
        optionsItem = tempDom[i].querySelectorAll(
          quesType == 6
            ? markType == 2
              ? `.mark-item`
              : `.custom-td`
            : `.score-option-item`
        );
        //兜底策略
        if (optionsItem.length == 0 && quesType == 6) {
          if (tempDom[i].querySelectorAll(".mark-item").length) {
            optionsItem = tempDom[i].querySelectorAll(".mark-item");
            if(item.markType != 2){
              markType=2;
            }
          }
          if (tempDom[i].querySelectorAll(".custom-td").length) {
            optionsItem = tempDom[i].querySelectorAll(".custom-td");
            if(item.markType != 1){
              markType = 1;
            }
          }
          optionsItem.length || (optionsItem = tempDom[i].querySelectorAll(".score-option-item"))
          optionsItem.length || (optionsItem = tempDom[i].querySelectorAll(".write-score-item"))
        }
        subQuesScoreBox = getSplitPoints(tempDom[i], "app");
        //该题下有打分框
        if (optionsItem.length > 0) {
          let firstScoreBox = [];
          // 简答题
          if (quesType == 6) {
            let trElement = tempDom[i].querySelectorAll(".custom-tr");
            //分数格
            if (markType == 1) {
              //十个位分开
              if (gridType == 1) {
                const trArray = Array.from(trElement[0].children);
                const splitTdIndices = trArray.reduce((indices, ite, inde) => {
                  if (ite.style.borderLeftWidth !== "") {
                    indices.push(inde);
                  }
                  return indices;
                }, []);
                // 十分位，个分位，小数位
                let scoreOne = [],
                  scoreTwo = [],
                  scoreThree = [];
                if (splitTdIndices.length == 2) {
                  [scoreOne, scoreTwo, scoreThree] = [
                    trArray.slice(0, splitTdIndices[0]),
                    trArray.slice(splitTdIndices[0], splitTdIndices[1]),
                    trArray.slice(splitTdIndices[1], trArray.length),
                  ];
                } else if (splitTdIndices.length == 1) {
                  [scoreTwo, scoreThree] = [
                    trArray.slice(0, splitTdIndices[0]),
                    trArray.slice(splitTdIndices[0], trArray.length),
                  ];
                } else {
                  scoreThree = trArray;
                }
                if (scoreOne.length > 0) {
                  const scoreTenFirst = `${item.id}_hasdec_${0}`;
                  let tenScores = [];
                  let firstScoreTenBox = [];
                  scoreOne[0].setAttribute("id", scoreTenFirst);
                  firstScoreTenBox = getPoints(scoreTenFirst, "app");
                  scoreOne.forEach((item, index) => {
                    tenScores.push(
                      item.outerText == "" ? -1 : Number(item.outerText)
                    );
                  });
                  //十分位坐标
                  let posTen = [
                    firstScoreTenBox[0],
                    firstScoreTenBox[1],
                    firstScoreTenBox[2] * scoreOne.length,
                    firstScoreTenBox[3],
                  ];
                  let scoreListTen = {
                    rows: trElement.length,
                    cols: scoreOne.length,
                    scores: tenScores,
                    pos: posTen,
                  };
                  scoresList.push(scoreListTen);
                }
                const scoreDecFirst = `${item.id}_hasdec_${1}`;
                let decScores = [];
                let firstScoreDecBox = [];
                if (scoreTwo.length > 0) {
                  //个分位第一个td id
                  scoreTwo[0].setAttribute("id", scoreDecFirst);
                  //个分位第一个td坐标
                  firstScoreDecBox = getPoints(scoreDecFirst, "app");
                  scoreTwo.forEach((item, index) => {
                    decScores.push(
                      item.outerText == "" ? -1 : Number(item.outerText)
                    );
                  });
                  //个分位坐标
                  let posDec = [
                    firstScoreDecBox[0],
                    firstScoreDecBox[1],
                    firstScoreDecBox[2] * scoreTwo.length,
                    firstScoreDecBox[3],
                  ];
                  let scoreListDec = {
                    rows: trElement.length,
                    cols: scoreTwo.length,
                    scores: decScores,
                    pos: posDec,
                  };
                  scoresList.push(scoreListDec);
                }
                //小数位
                const scoreSinFirst = `${item.id}_hasdec_${splitTdIndices[splitTdIndices.length - 1]
                  }`;
                let sinScores = [];
                let firstScoreSinBox = [];
                // 小数位第一个td id
                scoreThree[0].setAttribute("id", scoreSinFirst);
                // 小数位第一个坐标
                firstScoreSinBox = getPoints(scoreSinFirst, "app");
                scoreThree.forEach((item, index) => {
                  sinScores.push(
                    item.outerText == "" ? -1 : Number(item.outerText)
                  );
                });
                // 小数位坐标
                let posSin = [
                  firstScoreSinBox[0],
                  firstScoreSinBox[1],
                  firstScoreSinBox[2] * scoreThree.length,
                  firstScoreSinBox[3],
                ];
                let scoreListSin = {
                  rows: trElement.length,
                  cols: scoreThree.length,
                  scores: sinScores,
                  pos: posSin,
                };
                scoresList.push(scoreListSin);
              } else {
                //十个位不分开

                const trArray = Array.from(
                  trElement[[trElement.length - 1]].children
                );
                //是否有小数
                const splitTdIndices = trArray.reduce((indices, ite, inde) => {
                  if (ite.style.borderLeftWidth !== "") {
                    indices.push(inde);
                  }
                  return indices;
                }, []);
                let scores = [];
                let totalTd = trElement[trElement.length - 1].childElementCount;
                let id = `${item.id}_${trElement.length - 1}`;
                // 以最后一行最后一个td为基准
                trElement[trElement.length - 1].lastElementChild.setAttribute(
                  "id",
                  id
                );
                let lastTdPos = getPoints(id, "app");
                let pos = [
                  subQuesScoreBox[0], //x
                  lastTdPos[1] - (trElement.length - 1) * lastTdPos[3], //y
                  subQuesScoreBox[2], //w
                  lastTdPos[3] * trElement.length, //h
                ];
                trElement.forEach((item, index) => {
                  let hasScoreTd = Array.from(item.children)
                    .map((ite) => {
                      if (ite.outerText != "") {
                        return Number(ite.outerText);
                      }
                    })
                    .filter((it) => {
                      return Number(it != undefined);
                    });
                  if (hasScoreTd.length < totalTd) {
                    let nums = totalTd - hasScoreTd.length;
                    for (let i = 0; i < nums; i++) {
                      hasScoreTd.unshift(-1);
                    }
                  }
                  scores = [...scores, ...hasScoreTd];
                });
                let tenScoreList = [],
                  sinScoreList = [],
                  sinScore = scores[scores.length - 1];
                if (splitTdIndices.length > 0) {
                  //组装小数位坐标
                  sinScoreList = [
                    {
                      rows: 1,
                      cols: 1,
                      scores: [sinScore],
                      pos: lastTdPos,
                    },
                  ];
                  scores[scores.length - 1] = -1;
                }
                //十分位坐标
                tenScoreList = [
                  {
                    rows: trElement.length,
                    cols: totalTd,
                    scores: scores,
                    pos: pos,
                  },
                ];
                scoresList = [...tenScoreList, ...sinScoreList];
              }
            } else {
              //对错
              let scores = [Number(item.score), Number(item.score / 2), 0];
              let id = `${item.id}_${0}_mark`;
              optionsItem[0].setAttribute("id", id);
              firstScoreBox = getPoints(id, "app");
              scoresList = [
                {
                  rows: 1,
                  cols: optionsItem.length,
                  scores: scores,
                  pos: [
                    firstScoreBox[0],
                    firstScoreBox[1],
                    firstScoreBox[2] * optionsItem.length,
                    firstScoreBox[3],
                  ],
                },
              ];
            }
          } else {
            //填空题
            let scores = [];
            // 第一个打分框位置
            optionsItem.forEach((ite, ind) => {
              // 获取第一个打分框位置
              if (ind == 0) {
                let id = `${item.id}_${ind}`;
                ite.setAttribute("id", `${item.id}_${ind}`);
                firstScoreBox = getPoints(id, "app");
              }
              scores.push(ite.outerText == "" ? -1 : Number(ite.outerText));
            });
            if (markType == 2) {
              scores = [Number(item.score), 0];
            }
            //仅标识错误
            if (markType == 3) {
              scores = [0];
              missScore = Number(item.score);
            }
            scoresList = [
              {
                rows: Math.ceil(optionsItem.length / 23),
                cols: optionsItem.length,
                scores: scores,
                pos: [
                  firstScoreBox[0],
                  firstScoreBox[1],
                  firstScoreBox[2] * optionsItem.length,
                  firstScoreBox[3],
                ],
              },
            ];
          }
        }else{
          //识别对错 且 手阅
          if ((scoreType == 15 || isAIscan)) {
            if(cardType == 1 && !ansToTopConfig.fill){
              let lines = tempDom[i].querySelectorAll(".fill-scan");
              lines.forEach((line, index) => {
                const id = `line_${item.id}_${i}_${index}`;
                line.setAttribute("id", id);
                const lineBox = getPoints(id, "app");
                scoresList.push({
                  scores: item.lineList[0].score||"0",
                  pos: lineBox,
                })
                item.lineList.shift()
              });
            }
            // else{
            //   scoresList = [{
            //     scores: item.lineList[0].score||"0",
            //     pos: [
            //       subQuesScoreBox[0],
            //       subQuesScoreBox[1],
            //       subQuesScoreBox[2],
            //       subQuesScoreBox[3],
            //     ],
            //   }];
            //   item.lineList.shift()
            // }
          }
        }
        if (![1, 2, 8].includes(Number(quesType))) {
          //主观题区域高度增加2mm
          subQuesScoreBox[3] = subQuesScoreBox[3] + 2;
        }
        if ([3,7].includes(Number(quesType)) && (cardType == 0 || cardType == 2 || cardType == 4 || ansToTopConfig.fill)) {
          let width = 10;
          if(cardType == 4){
            width = 25;
          }
          subQuesScoreBox[0] = subQuesScoreBox[0] + width;
          subQuesScoreBox[2] = subQuesScoreBox[2] - width;
        }
        pointData = {
          // 划线打分题
          item_type: scoreType,
          //题目坐标
          pos: [
            subQuesScoreBox[0],
            subQuesScoreBox[1],
            subQuesScoreBox[2],
            subQuesScoreBox[3],
          ],
          question_no: Number(item.quesNo),
          question_id: item.id,
          // 可以是5-10合并题目给分
          question_nos: quesName,
          //题型
          question_type: Number(quesType),
          total_score: Number(item.score),
          miss_score: missScore,
          score_list: scoresList,
          word: item.answer,
        };
        optionData.push(pointData);
      }
    }
  });
  return optionData;
}

/**
 * @name:根据id获取相对原点坐标
 */
function getPoints(el, zero) {
  //原点坐标
  const initElement = document.getElementById(zero);
  const initPoint = initElement.getBoundingClientRect();
  //当前需要计算的坐标
  const currentDom = document.getElementById(el);
  if (!currentDom) {
    return []
  }
  let box = [];
  const currentPoints = currentDom.getClientRects();
  //如果包含多个坐标 且 只有一个子级  认为出现换行标签
  if(currentPoints.length > 1 && currentDom.classList.contains('fill-scan')){
    Array.from(currentPoints).forEach((point)=>{
      box.push(getPxConversionMm(point.left - initPoint.left))
      box.push(getPxConversionMm(point.top - initPoint.top + initElement.scrollTop))
      box.push(getPxConversionMm(point.width))
      box.push(getPxConversionMm(point.height))
    })
  }else{
    const currentPoint = currentDom.getBoundingClientRect();
    box = [
      getPxConversionMm(currentPoint.left - initPoint.left),
      getPxConversionMm(currentPoint.top - initPoint.top + initElement.scrollTop),
      getPxConversionMm(currentPoint.width),
      getPxConversionMm(currentPoint.height),
    ];
  }
  return box;
}

/**
 * @name:根据dom元素获取相对原点坐标
 */
function getSplitPoints(el, zero) {
  //原点坐标
  const initElement = document.getElementById(zero);
  const initPoint = initElement.getBoundingClientRect();
  //当前需要计算的坐标
  // const currentDom = document.getElementById(el);
  const currentPoint = el.getBoundingClientRect();
  const box = [
    getPxConversionMm(currentPoint.left - initPoint.left),
    getPxConversionMm(currentPoint.top - initPoint.top + initElement.scrollTop),
    getPxConversionMm(currentPoint.width),
    getPxConversionMm(currentPoint.height),
  ];
  return box;
}

/**
 * @name:px转mm
 */
function getPxConversionMm(value) {
  let conversion = (value * 25.4) / 96;
  if(conversion < 0){
   conversion = 0; 
  }
  return conversion;
}

/**
 * @name:计算页数
 */
function computePage(points) {
  let page = [];
  const height = 297.38 * 1;
  points.forEach((item) => {
    let quesHeight = JSON.parse(
      JSON.stringify(Math.ceil(item.pos[1] / height))
    );
    //当前题目所在页数
    let currentPage = Math.ceil(item.pos[1] / height);
    let obj = {};
    // obj[Math.ceil(item.pos[1] / height)] = item;
    //y坐标减去页面高度
    //需要减去的高度
    const needSubHeight = height * (currentPage - 1);
    // 整题y坐标
    item.pos[1] = item.pos[1] - needSubHeight;
    if (item.option_list) {
      //填涂题选项的y坐标
      item.option_list.forEach((ite) => {
        ite[1] = ite[1] - needSubHeight;
      });
    }
    if (item.score_list) {
      //划线题打分格的y坐标
      item.score_list.forEach((ite) => {
        let count = ite.pos.length / 4;
        while (count) {
          const i = 1 + 4 * (count - 1);
          ite.pos[i] = ite.pos[i] - needSubHeight;
          count--;
        }
      });
    }
    //x坐标
    // A3两栏
    obj[quesHeight] = item;
    page.push(obj);
  });
  return page;
}

/**
 * @name:计算考号位置
 * @returns
 */
const computeStuNo = (numberLayout) => {
  const domId = "stu-no-three-tr";
  let point = getPoints(domId, "app");
  let dom = document.getElementById(domId);
  let stuNo = {};
  const domXID = "stu-num-x";
  let pointX = getPoints(domXID, "app");
  if (numberLayout == 1 || numberLayout == 3 || numberLayout == 4 || numberLayout == 5) {
    //准考证
    let rows = 1;
    let cols = 1;
    let item_type = 2;//填涂考号
    if(numberLayout == 1){
      rows = dom.children[0].children.length;
      cols = dom.cells.length;
    }else{
      rows = dom.children[0].children.length;
      cols = dom.children[0].children[0].children.length;
      item_type = 14;//手写考号
    }
    
    stuNo = {
      item_type: item_type,
      pos: point,
      // 行数
      rows: rows,
      // 列数
      cols: cols,
      numX: pointX
    };
  } else {
    //条形码
    stuNo = {
      item_type: 6,
      pos: point,
    };
  }
  return stuNo;
};


/**
 * @name:定位点坐标
 * @returns
 */
const computePoints = (pagelayout) => {
  let box =
    pagelayout == IPAGELAYOUT.a33
      ? [4, 7, 4, 6]
      : pagelayout == IPAGELAYOUT.A3
        ? [7, 7, 7, 6]
        : [2, 7, 2, 6];
  return box;
};

/**
 * @name:辅助定位点坐标
 * @returns
 */
const computeLocatePoint = () => {
  const domIds = ["locatePoint-l-new", "locatePoint-r-new"];
  let points = [];
  domIds.forEach(domId => {
    let point = getPoints(domId, "app");
    points.push(point);
  });
  const locatePoint = {
    item_type: 21,
    pos_list: points,
  };
  return locatePoint;
};
/**
 * @name:获取二维码坐标
 * @returns
 */
const computeQrcode = () => {
  const domId = "qrcode1";
  let point = getPoints(domId, "app");
  const qrcode = {
    item_type: 1,
    pos: point,
    content: "",
  };
  return qrcode;
};

/**
 * @name:获取注意事项坐标
 * @returns
 */
const computeNoticeInfo = (pageLayout, numberLayout) => {
  // numberLayout 1.准考证号 2.条形码
  const domId = "notice";
  let point = getPoints(domId, "app");
  const noticePoints = {
    item_type: 5,
    pos: point,
    content: "",
  };
  return noticePoints;
};

/**
 * @name:根据key分割对象
 * @param data
 * @returns
 */
function groupValuesByKey(data) {
  const tempValue = {};
  for (let obj of data) {
    const key = Object.keys(obj);
    const value = obj[key];
    if (tempValue[key]) {
      tempValue[key].push(value);
    } else {
      tempValue[key] = [value];
    }
  }
  return tempValue;
}

/**
 * @name:还原100%分辨率，保留四位小数
 */
const restoreRatio = (data) => {
  const ratio = 1;
  data.margin.pos = data.margin.pos.map((item) => {
    return Number((item / ratio).toFixed(4));
  });
  data.pages.forEach((page) => {
    page.forEach((item) => {
      item.pos && (item.pos = item.pos.map((ite, inde) => {
        return Number((ite / ratio).toFixed(4));
      }));
      item.pos_list &&
        item.pos_list.forEach((ite, index) => {
          item.pos_list[index] = ite.map((it) => {
            return Number((it / ratio).toFixed(4));
          });
        });
      item.option_list &&
        item.option_list.forEach((ite, index) => {
          item.option_list[index] = ite.map((it) => {
            return Number((it / ratio).toFixed(4));
          });
        });
      item.score_list &&
        item.score_list.forEach((ite) => {
          ite.pos = ite.pos.map((it) => {
            return Number((it / ratio).toFixed(4));
          });
        });
    });
  });
  return data;
};

/**
 * @name:获取缺考标记坐标
 */
const computeMissExamMark = () => {
  const domId = "miss-exam-mark";
  let point = getPoints(domId, "app");
  const missMark = {
    item_type: 8,
    pos: point,
    content: "",
  };
  return missMark;
};

/**
 * @name:获取AB卷标记坐标
 */
const computeABMark = () => {
  let point = getPoints("notice-mark", "app");
  let aPoint = getPoints("ab-a-mark", "app");
  let bPoint = getPoints("ab-b-mark", "app");
  const abMark = {
    item_type: 19,
    pos: point,
    option_list: [aPoint, bPoint],
    content: "",
  };
  return abMark;
};

/**
 * @name:获取姓名位置坐标
 */
const computestuName = () => {
  const domId = "stu-name";
  let point = getPoints(domId, "app");
  const missMark = {
    item_type: 9,
    pos: point,
    content: "",
  };
  return missMark;
};

/**
 * @name:获取班级位置坐标
 */
const computeClsName = () => {
  const domId = "class-name";
  let point = getPoints(domId, "app");
  const missMark = {
    item_type: 17,
    pos: point,
    content: "",
  };
  return missMark;
};

/**
 * @name:获取选做题填涂区坐标
 */
const computeChooseDo = (item, cardType) => {
  let domId = 'app'
  let optionData = [];
  let sort = 0;
  for (let i = 0; i < item.doQuesList.length; i++) {
    const dItem = item.doQuesList[i];
    if (cardType == 1) {
      let pos = getPoints(`choose_${dItem.ids.join('_')}`, domId);
      let optList = [];
      dItem.ids.forEach(id => {
        let opt = getPoints(`choose_${id}`, domId);
        optList.push(opt);
      })
      let count = dItem.doCount;
      const pointData = {
        "pos": pos,
        "item_type": 18,
        "option_list": optList,
        "question_nos": `${dItem.name.join(',')}(${dItem.ids.length}选${count})`,
        "question_no": chooseQuesNo++,
        "question_ids": dItem.ids,
        "select_count": count, //题卡合一是多个，网阅是1个
        "do_count": dItem.doCount,
        "sort":sort++
      };
      optionData.push(pointData);
    } else {
      for (let index = 0; index < dItem.doCount; index++) {
        let pos = getPoints(dItem.targetIds[index], domId);
        let optList = [];
        dItem.ids.forEach(id => {
          let opt = getPoints(`choose_${id}_${dItem.ids[index]}`, domId);
          optList.push(opt);
        })
        let count = dItem.doCount;
        const pointData = {
          "pos": pos,
          "item_type": 18,
          "option_list": optList,
          "question_nos": `${dItem.name.join(',')}(${dItem.ids.length}选${count})`,
          "question_no": chooseQuesNo,
          "question_ids": dItem.ids,
          "id": dItem.ids[index],
          "select_count": 1, //题卡合一是多个，网阅是1个
          "do_count": dItem.doCount,
          "sort":sort++
        };
        optionData.push(pointData);
      }
      chooseQuesNo++;
    }
  }
  return optionData;
}


const conversion_getDPI = () => {
  const arrDPI = new Array();
  if (window.screen.deviceXDPI) {
    arrDPI[0] = window.screen.deviceXDPI;
    arrDPI[1] = window.screen.deviceYDPI;
  } else {
    const tmpNode = document.createElement("DIV");
    tmpNode.style.cssText =
      "width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden";
    document.body.appendChild(tmpNode);
    arrDPI[0] = tmpNode.offsetWidth;
    arrDPI[1] = tmpNode.offsetHeight;
    tmpNode.parentNode.removeChild(tmpNode);
  }
  return arrDPI;
}
const mmConversionPx = (value) => {
  const inch = value / 25.4;
  const c_value = inch * conversion_getDPI()[0];
  return c_value;
}