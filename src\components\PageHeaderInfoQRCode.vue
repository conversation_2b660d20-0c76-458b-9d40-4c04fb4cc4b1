<template>
  <div style="
      font-size: 4.8mm;
      line-height: 4mm;
      font-family: 宋体, <PERSON>mSun, Times New Roman;
    ">
    <div v-if="!Paper.isSealLine && Paper.pageLayout != IPAGELAYOUT.A3  && ![IPAGELAYOUT.A4,IPAGELAYOUT.A42].includes(Paper.pageLayout)" :class="['header-head', Paper.isEdit ? 'uneditstyle' : '']">
      <div :id="'name-txt' + page" class="name-txt" :style="{
        fontSize: nameSize + ' !important',
        fontWeight: nameFont ? 'bold !important' : 'unset !important',
        maxHeight: '12mm !important',
        lineHeight: lineHeight + ' !important',
      }" :contenteditable="true" v-html="Paper.nameHtml.html"></div>
      <div class="nameset nosave">
        <el-checkbox v-model="nameFont" label="加粗" size="small" />
        <el-select v-model="nameSize" style="width: 80px" :teleported="false" placeholder="Select" size="small">
          <el-option v-for="item in fontSizeList" :key="item.value" :label="item.text" :value="item.value" />
        </el-select>
      </div>
    </div>
    <div class="header-table abridge" style="display: flex;justify-content: space-around;">
      <div class="headerinfo left" :style="{
        width: 'fit-content',
        display: 'inline-block'
      }">
        <div class="header-info">
          <div :id="'qrcode' + page" class="qrcode" :page="page"
            style="width: 20mm; height: 20mm; display: inline-block">
            <img class="nosave" v-if="QRCodeUrl" :src="QRCodeUrl" style="width: 20mm; height: 20mm" />
          </div>
        </div>
      </div>
      <template v-if="Paper.isSealLine">
        <div :class="['header-head', Paper.isEdit ? 'uneditstyle' : '']">
          <div :id="'name-txt' + page" class="name-txt" :style="{
            fontSize: nameSize + ' !important',
            fontWeight: nameFont ? 'bold !important' : 'unset !important',
            maxHeight: '12mm !important',
            lineHeight: lineHeight + ' !important',
          }" :contenteditable="true" v-html="Paper.nameHtml.html"></div>
          <div class="nameset nosave">
            <el-checkbox v-model="nameFont" label="加粗" size="small" />
            <el-select v-model="nameSize" style="width: 80px" :teleported="false" placeholder="Select" size="small">
              <el-option v-for="item in fontSizeList" :key="item.value" :label="item.text" :value="item.value" />
            </el-select>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="headerinfo right" style="vertical-align: top">
          <template v-if="Paper.pageLayout == IPAGELAYOUT.A3  || [IPAGELAYOUT.A4,IPAGELAYOUT.A42].includes(Paper.pageLayout)">
            <div :class="['header-head', Paper.isEdit ? 'uneditstyle' : '']" style="height: 12mm;">
          <div :id="'name-txt' + page" class="name-txt" :style="{
            fontSize: nameSize + ' !important',
            fontWeight: nameFont ? 'bold !important' : 'unset !important',
            maxHeight: '12mm !important',
            lineHeight: lineHeight + ' !important',
          }" :contenteditable="true" v-html="Paper.nameHtml.html"></div>
          <div class="nameset nosave">
            <el-checkbox v-model="nameFont" label="加粗" size="small" />
            <el-select v-model="nameSize" style="width: 80px" :teleported="false" placeholder="Select" size="small">
              <el-option v-for="item in fontSizeList" :key="item.value" :label="item.text" :value="item.value" />
            </el-select>
          </div>
        </div>
          </template>
          <div class="stu-info-wrap m-b-4" name="sealLine" style="line-height: 7mm">
            <div :style="{
              // marginTop: Paper.correctType == ICorrectType.WEB ? '0mm' : '5mm',
              // marginBottom: Paper.correctType == ICorrectType.WEB ? 0 : '8mm',
            }">
              <div style="display: inline-block" id="class-name">
                <span style="width: 10mm; padding-left: 2mm">班&nbsp;级：</span>
                <div class="card-color-info"></div>
              </div>
              <div style="display: inline-block" id="stu-name">
                <span style="width: 10mm; padding-left: 2mm">姓&nbsp;名：</span>
                <div class="card-color-info"></div>
              </div>
            </div>
            <!-- <div style="margin-top: 4mm" v-if="Paper.correctType == ICorrectType.WEB && (Paper.pageLayout != IPAGELAYOUT.A33 && Paper.pageLayout != IPAGELAYOUT.A32)">
              <div style="display: inline-block">
                <span style="width: 10mm; padding-left: 2mm">考场号：</span>
                <div class="card-color-info"></div>
              </div>
              <div style="display: inline-block">
                <span style="width: 10mm; padding-left: 2mm">座位号：</span>
                <div class="card-color-info"></div>
              </div>
            </div> -->
          </div>
        </div>
      </template>

      <!-- <div id="notice">
        <div class="stu-bar-code-container" id="stu-no-three-tr" style="width: 50mm;height: 20mm;padding: 0;border: 1px dashed #000;">
          <div class="stu-bar-code">
            <div class="text text--top gray"><span>学生信息粘贴区</span></div>
            <div class="text text--bottom gray">
              <span>(正面朝上，切勿贴出虚线方框)</span>
            </div>
          </div>
        </div>
      </div> -->
      <div class="header-write" style="text-align: center;margin-top: 0mm;">
        <div style="font-size: 14px;line-height: 8mm;text-align: left;">学号</div>
        <table id="stu-no-three-tr" style="display: inline-block;">
          <tr>
            <td style="width: 8mm;height: 10mm;border: 1px solid;" class="write-box" v-for="item in Paper.stuNoLength" :style="{
              borderRight: item === Paper.stuNoLength ? '1px solid' : '1px dashed',
              borderLeft: item === 1 ? '1px solid' : '1px dashed',
              borderTop: '1px solid',
              borderBottom: '1px solid'
            }">
            </td>
          </tr>
        </table>
      </div>
    </div>

  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  ref,
  nextTick,
  watch,
} from "vue";
import Paper from "../../src/views/paper";
import QRCode from "qrcode";
import { INUMBERLAYOUT, ICorrectType, IPAGELAYOUT } from "@/typings/card";
import NoticeVue from "./Notice.vue";
export default defineComponent({
  props: {
    id: {
      type: String,
      default: "",
    },
    page: {
      type: Number,
      default: 1,
    },
  },
  components: {
    NoticeVue,
  },
  setup(props: any, ctx: any) {
    const state = reactive({
      Paper: Paper,
      INUMBERLAYOUT,
      IPAGELAYOUT,
      ICorrectType,
      qrValue: "",
      QRCodeUrl: "",
      //字号集合
      fontSizeList: [
        {
          id: 1,
          text: "小五",
          value: "3.2mm",
        },
        {
          id: 2,
          text: "五号",
          value: "3.5mm",
        },
        {
          id: 3,
          text: "小四",
          value: "3.7mm",
        },
        {
          id: 4,
          text: "四号",
          value: "4mm",
        },
        {
          id: 5,
          text: "小三",
          value: "4.2mm",
        },
        {
          id: 6,
          text: "三号",
          value: "4.4mm",
        },
        {
          id: 7,
          text: "小二",
          value: "4.8mm",
        },
      ],
      nameSize: "4.8mm",
      nameFont: true,
      lineHeight: "4.8mm",
    });

    watch(
      () => [state.nameSize, state.nameFont],
      () => {
        Paper.nameHtml.fontBold = state.nameFont;
        Paper.nameHtml.size = state.nameSize;
        if (Number(state.nameSize.replace("mm", "")) > 4) {
          state.lineHeight = "4.8mm";
        } else {
          state.lineHeight = "4mm";
        }
      }
    );

    /**
     * 页面一开始加载
     */
    onMounted(async () => {
      state.nameFont = Paper.nameHtml.fontBold;
      state.nameSize = Paper.nameHtml.size;
      if (Number(state.nameSize.replace("mm", "")) > 4) {
        state.lineHeight = "4.8mm";
      } else {
        state.lineHeight = "4mm";
      }
      state.qrValue = Paper.paperId + props.page.toString().padStart(2, "0");
      let opts = {
        errorCorrectionLevel: "M",
        type: "image/jpeg",
        width: "20mm",
        height: "20mm",
        quality: 0.8,
        margin: 0,
        color: {
          dark: "#020202",
          light: "#fff",
        },
      };
      state.QRCodeUrl = await QRCode.toDataURL(state.qrValue, opts);
      nextTick(() => {
        const contenteditableDiv = document.getElementById(
          "name-txt" + props.page.toString()
        );
        contenteditableDiv?.addEventListener("paste", function (e: any) {
          e.preventDefault();
          document.execCommand(
            "insertHTML",
            false,
            e?.clipboardData.getData("text")
          );
        });
        contenteditableDiv?.addEventListener("blur", function (e: any) {
          e.target.innerHTML = e.target.innerHTML.replace(
            /(style)="[^"]*"/g,
            ""
          );
          Paper.name = e.target.textContent;
          Paper.nameHtml = {
            html: e.target.innerHTML,
            size: state.nameSize,
            fontBold: state.nameFont,
          };
          Array.from(document.getElementsByClassName("name-txt")).forEach(
            (el: any) => {
              el.innerHTML = Paper.nameHtml.html;
            }
          );
          // $(".name-txt").text(Paper.name)
        });
      });
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style lang="scss" scoped>
.header {
  padding: 0;
  margin: 0;
  margin-bottom: 1mm;
  border-top: 0.1mm solid #fff;
  border-bottom: 0.1mm solid #000;
  position: relative;
  width: 100%;
  font-family: 宋体, SimSun, Times New Roman;
}

.header-table {
  width: 100%;
  display: flex;
  padding-top: 2mm;
  text-align: center;

  .headerinfo {
    display: inline-block;
  }

  .left {
    width: 110mm;
  }

  .right {
    // width: calc(100% - 110mm);
    position: relative;
  }

  tr {
    width: 100%;
  }

  td {
    vertical-align: inherit;
  }

  .header-info {
    display: flex;
  }

  .card-color-info {
    display: inline-block;
    width: 20mm;
    border-bottom: 0.1mm solid;
  }
}

.header-name {
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 100%;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  max-height: 21mm;
  width: 100%;
}

.name-txt {
  display: block;
  font-size: 4.3mm;
  max-width: 100%;
  margin: 0 auto;
  font-weight: 700;
  word-break: break-all;
  text-align: center;
  overflow: hidden;
  -webkit-line-clamp: 1;
  line-height: 6.5mm;
  min-height: 6.5mm;
  font-family: 宋体, SimSun, Times New Roman;

  &:hover {
    outline: 1px solid #000;
  }

  &:hover~.nameset {
    visibility: inherit;
  }
}

.nameset {
  visibility: hidden;
  position: absolute;
  top: 13px;
  right: 0;
  z-index: 9999;

  &:hover {
    visibility: inherit;
  }
}

.flex-end {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.m-b-4 {
  // margin-bottom: 4mm !important;
}

.flex-center {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.barcode-wrapper {
  width: 65mm;
}

.stu-bar-code {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  width: 100%;
  height: 100%;
  background-color: #e4e4e4;

  .text {
    color: #a5a5a5;
  }

  .text--top {
    font-size: 20px;
  }

  .text--bottom {
    font-size: 12px;
  }
}

.stu-no-table {
  border-collapse: collapse;
  padding: 0;
  margin: 0 auto;
  text-align: center;
  color: #000;
}

.stu-no-first-tr {
  height: 4mm;
}

.stu-num {
  width: 30mm;
  font-size: 3mm !important;
  border: 0.1mm solid #000;
  border-collapse: collapse;
  text-align: center;
}

.stu-no-second-tr {
  height: 5mm;
}

.stu-no-three-tr {
  margin: 0;
  padding: 0;
}

.stu-tb-left {
  border: 0.1mm solid #000;
  border-collapse: collapse;
  padding-top: 0;
  border-bottom-color: #000;
  width: 6.6mm;
  color: #000;
  border-left-color: #000;
}

.stu-num-td {
  border: 0.1mm solid #000;
  border-collapse: collapse;
  padding-top: 0;
  border-bottom-color: #000;
  width: 6.6mm;
  color: #000;

  &:last-child {
    border-right-color: #000;
  }
}

.td-flex {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.table-left-td {
  height: 100%;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.table-notice-div {
  width: 100%;
}

.table-left-td,
.table-notice-div {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}

.stu-info-wrap {
  padding-top: 2mm;
  text-align: center;
  position: relative;
  font-size: 3.5mm !important;
}

.notice-info {
  display: inline-block;
  width: 100%;
  height: 21mm;
  max-width: 90mm;
  text-align: left;
  border: 0.1mm solid #000;
  line-height: 4mm;
  padding: 0;
  word-break: break-all;
  overflow: hidden;
}

.notice-title {
  text-align: center;
  font-size: 3.5mm;
  margin: 0;
  padding: 0;
  color: #000;
}

.notice-infop {
  margin: 0 1mm;
  padding: 0;
  color: #000;
}

.card-color {
  color: #000;
}

.full-wrap {
  margin-top: 0mm;
  max-width: 90mm;
  width: 100%;
  line-height: 7mm;
  font-size: 3mm !important;
  border: 0.1mm solid #000;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}

.full-item {
  vertical-align: middle;

  span {
    margin-right: 3mm;
  }
}

.full-sec {
  display: inline-block;
  border-width: 1.5mm 2.5mm;
  border-style: solid;
  border-color: #000;
  width: 0;
  height: 0;
}

.full-example {
  margin: 0;
  padding: 0;
  width: 5mm;
  height: 3mm;
  border: 0.1mm solid #000;
  font-size: 5mm;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}

.stu-num-full-sec {
  position: relative;
  // display: inline-block;
  line-height: 4.1mm;
  vertical-align: bottom;
  border: 0 solid #000;
  width: 6.6mm;
  height: 4.1mm;
  padding: 0;
  margin: 0;
  font-size: 2.5mm !important;
  font-family: 宋体, STSong, SimSun;
}

.stu-num-txt {
  margin: 0;
  padding: 0 0.6mm;
}

.empty-th {
  border: 0.1mm solid #000;
  border-collapse: collapse;
  font-size: 2.5mm !important;
}
</style>
<style lang="scss">
.headerinfo {
  .el-input__inner {
    height: 30px !important;
    line-height: 30px !important;
    // padding: 1.2mm 1mm !important;
  }
}

.ques-list {
  .el-input__inner {
    // text-align: center;
  }
}

.preview .qrcode {
  background: url(../assets/qrcode.png) 100% 100%;
  background-size: cover;
  // background: url('../assets/qrcode.png') no-repeat;
}
</style>
