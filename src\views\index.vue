<template>
  <p v-if="Paper.isEdit || isShowTip" style="
      text-align: center;
      position: fixed;
      width: 100%;
      z-index: 99;
      background: rgb(255, 221, 26, 0.6);
      font-size: 12px;
      line-height: 24px;
      color: rgb(209, 0, 0);
    ">
    <span v-if="Paper.isEdit"> 温馨提示：制卡已完成，无法修改 </span>
    <span v-else> 为保证制卡效果，请将浏览器缩放比还原为100%！ </span>
  </p>
  <div v-show="!showPreview" class="c30-teach-card-container">
    <div class="c30-teach-card-main" v-if="!loading">
      <div class="content">
        <div :class="layoutClass[Paper.pageLayout]" id="content">
          <component :data="Paper" :is="activeCardComponent" />
        </div>
      </div>
      <div class="silder noselect">
        <silder-set :data="info" :cardType="cardType" @preview="preview"></silder-set>
      </div>
    </div>
  </div>
  <div v-if="showPreview" class="close" @click="showPreview = false"></div>
  <div v-if="showPreview" class="downPreview" @click="downloadEvent">
    下载预览
  </div>
  <preview v-if="showPreview"></preview>
  <div style="position: absolute;top: 0;z-index: 99999;">
    <el-button v-if="showPlay" @click="closePlay">关闭</el-button>
    <!-- <el-button v-if="!isProd && !showPlay" @click="replay">回放</el-button> -->

  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted, ref, computed, watch } from 'vue';
import {
  getExamPaperInfo,
  getQuesList,
  getWorJsonByPaper,
  getWorJsonByWork,
  getTestBankInfo,
  examCard,
  getPublicConfigBySchoolInfo,
  clearTestBankQueInfo,
  getAIScanSubjectAPI
} from '@/service/api';
import {
  ISOURCETYPE,
  QUES_TYPE,
  ICARDMODEL,
  IPAGELAYOUT,
  ICorrectType,
  INUMBERLAYOUT,
  MARK_TYPE,
  GRIDPLACE_TYPE,
  ANSWER_TYPE,
  IRULERTYPE,
  IEditerPowerType,
} from '@/typings/card';
import {
  arabToChinese,
  deepClone,
  generateUUID,
  isNullOrUndefined,
  toDecimal,
  getQueryString,
  base64toFile,
} from '@/utils/util';
import cardMain from './cardMain.vue';
import cardEnMain from './cardEnMain.vue';
import cardOnlyMain from './cardOnlyMain.vue';
import cardPhotoMain from './cardPhotoMain.vue';
import silderSet from './modules/silderSet.vue';
import { PaperManager } from './paper';
import Preview from '@/components/Preview.vue';
import { getBrowerZoom } from '@/utils/util';
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus';
import bus from '@/utils/bus';
// import bus from '@/utils/bus';
// import rrweb from 'rrweb';

const Paper = PaperManager.GetInstance();
declare let rrweb: any;
export default defineComponent({
  props: {
    //试卷id
    id: {
      type: String,
      default: '530b7c80-2159-45fb-8f97-d7765ace2fee',
    },
    cardType: {
      type: String,
      default: '1',
    },
    //是否编辑
    // isEdit: {
    //   type: String,
    //   default: '',
    // },
    // //试卷名称
    // title: {
    //   type: String,
    //   default: "八年级暑期综合素质提升营阶段测试",
    // },
    // //学校id
    // schoolId: {
    //   type: String,
    //   default: ssoClient.getUserInfo().schoolId,
    // },
    //来源
    sourceType: {
      type: String,
      default: ISOURCETYPE.HAND,
    },
    //学生考号长度
    stuNoLength: {
      type: Number,
      default: 8,
    },
    // //是否打印
    // print: {
    //   type: Number,
    //   default: 0,
    // },
  },
  components: {
    cardMain,
    cardEnMain,
    cardOnlyMain,
    cardPhotoMain,
    silderSet,
    Preview,
  },
  setup(props, cxt) {
    const state = reactive({
      loading: true,
      Paper: Paper,
      cardType: Paper.cardType,
      correctType: Paper.correctType,
      SOURCE_TYPE: ISOURCETYPE,
      ICARDMODEL: ICARDMODEL,
      info: {} as any,
      idKey: 'id',
      quesMap: {} as any,
      loadSuccess: false,
      mm: 0.264583,
      a4Height: 287,
      hHeight: 13.8,
      iHeight: 72,
      fHeight: 10,
      showPreview: false,
      isShowTip: getBrowerZoom() !== 1,
      layoutClass: { 1: 'a4', 2: 'a3', 3: 'a33', 4: 'a32', 5: 'a23', 6: 'a4 a42' },
      showPlay: false,
      isProd: process.env.VUE_APP_ENV == 'prod',
    });

    watch(() => state.showPreview, async () => {
      //通知父级页面完成制卡
      const data = {
        from: Paper.abType,
        type: 'hideSlider',
        arguments: state.showPreview,
      }
      window.parent.postMessage(JSON.stringify(data), '*');
    });

    /**
     * @description: 激活的卡片组件
     * @param {*} computed
     * @return {*}
     */
    const activeCardComponent = computed(() => {
      if (state.correctType == ICorrectType.PHOTO) {
        return "cardPhotoMain"
      }

      if (state.cardType == ICARDMODEL.QUESCARD) {
        return "cardMain"
      } else if (state.cardType == ICARDMODEL.ENGLISH) {
        return "cardEnMain"
      } else {
        return "cardOnlyMain"
      }
    })

    bus.on('switchCard', () => {
      state.cardType = Paper.cardType;
    });

    bus.on('switchCorrect', () => {
      state.correctType = Paper.correctType;
    });

    /**
     * @name 根据题目id获取题目详细信息
     */
    const findQuesInfo = async (quesIds: Array<string>) => {
      let result: any = await getQuesList({ qIds: quesIds.join(',') });
      if (result && result.code === 1) {
        let quesNo = 1;
        result = result.data;
        result.forEach((data: any) => {
          const qsList: any = [];
          let score = 0;
          data.data.qs.forEach((qs: any, index: number) => {
            let answer = qs.ans.toString();
            if(data.data.type == QUES_TYPE.fillEva){
              answer = qs.ans;
            }
            const ques = {
              id: data.qId,
              topic: qs.q_html,
              content: qs.desc_html,
              answer: answer,
              // analysis: qs.exp,
              optionText: qs.opts_htmls,
              typeId: data.data.type,
              score: parseFloat(qs.score),
              quesNos: qs.quesNo ? qs.quesNo : index + 1,
              quesNo: quesNo++,
              showType: qs.showType || 3,
            };
            if (props.sourceType == state.SOURCE_TYPE.WEB) {
              state.info.quesInfo.forEach((bigQuesIt: any) => {
                bigQuesIt.data.forEach((quesIt: any) => {
                  if (quesIt[state.idKey] === ques.id) {
                    ques.score = quesIt.score;
                  }
                });
              });
            }
            qsList.push(ques);
            score += ques.score;
          });

          const ques = {
            id: data.qId,
            levelCode: data.data.levelcode,
            typeId: data.data.type,
            topic: data.data.q_html,
            content: data.data.desc_html,
            score: score,
            qsList: qsList,
          };

          if (props.sourceType == state.SOURCE_TYPE.WEB) {
            state.info.quesInfo.forEach((bigQuesIt: any) => {
              bigQuesIt.data.forEach((quesIt: any) => {
                if (quesIt[state.idKey] === ques.id) {
                  ques.score = quesIt.score;
                }
              });
            });
          }
          state.quesMap[ques.id] = ques;
        });
        if (isNullOrUndefined(state.info.cardInfo)) {
          let fullScore = 0;
          state.info.quesInfo.forEach((bigQues: any) => {
            let score = 0;
            let count = 0;
            let typeId = QUES_TYPE.singleChoice;
            let isAverage = true;
            bigQues.data.forEach((ques: any, index: number) => {
              if (index === 0) {
                typeId = ques.typeId;
              }
              if (props.sourceType == state.SOURCE_TYPE.WEB) {
                score += ques.score;
                count++;
              } else {
                const quesInfo = state.quesMap[ques[state.idKey]];
                if(!quesInfo) {
                  console.error("有题目存在异常，请检查题目是否存在",ques[state.idKey]);
                  return;
                }
                if (!quesInfo.score) {
                  quesInfo.score = ques.score;
                }
                if (!ques.score) {
                  ques.score = quesInfo.score;
                }
                score += Number(ques.score);
                count += quesInfo.qsList.length;
                if (quesInfo.levelCode) {
                  ques.data.forEach((item: any, index: number) => {
                    // item.quesNos = quesInfo.qsList[index].quesNos || item.quesNos;
                    if(item.typeId == QUES_TYPE.fillEva){
                      item.answer = quesInfo.qsList[index].answer;
                    }else{
                      item.answer = quesInfo.qsList[index].answer.toString();
                    }
                    item.optionCount = quesInfo.qsList[index].optionText.length;
                    if (item.typeId == QUES_TYPE.judge) {
                      item.judgeType = Paper.judgeType;
                    }
                    item.step = Paper.step;
                    if (isAverage) {
                      isAverage = score / count == item.score;
                    }
                  });
                } else {
                  // ques.quesNos = quesInfo.qsList[0].quesNos || ques.quesNos;
                  if(ques.typeId == QUES_TYPE.fillEva){
                    ques.answer = quesInfo.qsList[0].answer;
                  }else{
                    ques.answer = quesInfo.qsList[0].answer.toString();
                  }
                  ques.optionCount = quesInfo.qsList[0].optionText.length;
                  if (ques.typeId == QUES_TYPE.judge) {
                    ques.judgeType = Paper.judgeType;
                  }
                  ques.step = Paper.step;
                  if (isAverage) {
                    isAverage = score / count == ques.score;
                  }
                }
                //初始化题目题卡数据
                //选择题
                if (ques.typeId == QUES_TYPE.singleChoice) {
                  bigQues.optionCount = Paper.optionCount;
                  bigQues.arrange = Paper.arrange;
                } else if (ques.typeId == QUES_TYPE.choice) {
                  //多选题
                  bigQues.halfScore = toDecimal(bigQues.quesScore / 2);
                  bigQues.ruleType = IRULERTYPE.STANDAD;
                  bigQues.optionCount = Paper.optionCount;
                  bigQues.arrange = Paper.arrange;
                } else if (ques.typeId == QUES_TYPE.judge) {
                  //判断题
                  bigQues.judgeType = Paper.judgeType;
                } else if (Paper.isFillQues(ques.typeId)) {
                  //填空题
                  bigQues.typeId = Paper.isFillQues(ques.typeId) ? QUES_TYPE.fill : ques.typeId;
                  bigQues.step = Paper.step;
                  bigQues.markType = MARK_TYPE.YESORNO;
                  bigQues.gridPlace = GRIDPLACE_TYPE.AFTER;
                  bigQues.arrange = Paper.arrange;
                  bigQues.lineType = Paper.lineType;
                  bigQues.data.forEach((small: any) => {
                    small.typeId = Paper.isFillQues(small.typeId) ? QUES_TYPE.fill : small.typeId;
                    small.isShowSetting = false;
                    small.step = Paper.step;
                    small.markType = MARK_TYPE.YESORNO;
                    small.gridPlace = GRIDPLACE_TYPE.AFTER;
                    small.lineType = Paper.lineType;
                    small.lineNum = Paper.lineNum;
                    small.data &&
                      small.data.forEach((ite: any) => {
                        ite.typeId = Paper.isFillQues(ite.typeId) ? QUES_TYPE.fill : ite.typeId;
                        ite.isShowSetting = false;
                        ite.markType = MARK_TYPE.YESORNO;
                        ite.gridPlace = GRIDPLACE_TYPE.AFTER;
                        ite.lineType = Paper.lineType;
                        ite.lineNum = Paper.lineNum;
                      });
                  });
                } else if (ques.typeId == QUES_TYPE.subject) {
                  //简答题
                  bigQues.step = Paper.step;
                  bigQues.markType = Paper.markType;
                  bigQues.gridType = Paper.gridType;
                  bigQues.decimal = Paper.decimal;
                  bigQues.hasLine = Paper.hasLine;
                  bigQues.wordNumber = Paper.wordNumber;
                  bigQues.isWriting = Paper.isWriting;
                  bigQues.cellSize = Paper.cellSize;
                  bigQues.rowNum = Paper.rowNum;
                  bigQues.cells = Paper.cells;
                  bigQues.data.forEach((small: any) => {
                    small.step = Paper.step;
                    small.markType = Paper.markType;
                    small.gridType = Paper.gridType;
                    small.decimal = Paper.decimal;
                    small.hasLine = Paper.hasLine;
                    small.rowNum = Paper.rowNum;
                    small.cellSize = Paper.cellSize;
                  });
                }
              }
            });
            bigQues.typeId = typeId;
            bigQues.score = score;
            bigQues.count = count;
            if (isAverage) {
              bigQues.quesScore = score / count;
            } else {
              bigQues.quesScore = '';
            }
            fullScore = fullScore + bigQues.score;
          });
        }
      }
      Paper.updateQuestions(state.info.quesInfo);
      Paper.quesInfos = state.info.quesInfo;
      Paper.items = state.info.items;
      Paper.questions = state.info.cardQueInfo || state.quesMap;
      if (Paper.cardType == ICARDMODEL.ONLYCARD) {
        Paper.questions = state.quesMap;
      }
      let quesInfos = Paper.convertQuesLevel3to2(deepClone(Paper.getQuesInfos()));
      Paper.updateAndMergeQuestions(quesInfos);
    };

    /**
     * @name 根据试卷id获取试卷信息
     */
    const findWorkInfo = async () => {
      let quesIds: string[] = [];
      if (
        props.sourceType == state.SOURCE_TYPE.PAPER ||
        props.sourceType == state.SOURCE_TYPE.HAND ||
        props.sourceType == state.SOURCE_TYPE.WEB
      ) {
        const result: any = await getWorJsonByPaper({ paperNo: Paper.paperId });
        if (result && result.code === 1) {
          Paper.isTemplate = result.data.isTemplate || 0;
          if (!Paper.isTemplate) {
            Paper.editerType = IEditerPowerType.NORMAL
          } else {
            const editPower = getQueryString('editPower');
            if (editPower == "1") {
              Paper.editerType = IEditerPowerType.TPL_HAS_POWER
            } else {
              Paper.editerType = IEditerPowerType.TPL_NO_POWER
            }
          }
          Paper.isABPaper = result.data.abCardType == 2;
          state.info.name = result.data.name;
          if(Paper.abType){
            state.info.name += Paper.abType + '卡';
          }
          state.info.subjectId = result.data.subjectId;
          state.info.gradeId = result.data.gradeId;
          // state.info.userId = result.data.userId;
          state.info.schoolId = result.data.schoolId;
          if (!isNullOrUndefined(result.data.cardInfo)) {
            Paper.isSaveData = true;
            state.info.cardInfo = JSON.parse(result.data.cardInfo);
            state.info.name = state.info.cardInfo.title;
            state.info.nameHtml = state.info.cardInfo.nameHtml;

            Paper.pageLayout = state.info.cardInfo.pageLayout;
            Paper.numberLayout = state.info.cardInfo.numberLayout;
            Paper.spreadQues = 1;
            Paper.cardType = state.info.cardInfo.cardType;
            state.cardType = Paper.cardType;
            Paper.isSealLine = state.info.cardInfo.isSealLine;
            Paper.isNumberHasX = state.info.cardInfo.isNumberHasX;
            Paper.fontSize = state.info.cardInfo.fontSize;
            Paper.space = state.info.cardInfo.space;
            Paper.fontFamily = state.info.cardInfo.fontFamily;
            Paper.headerName = state.info.cardInfo.headerName || state.info.schooldName;
            state.correctType = Paper.setCorrectType(state.info.cardInfo.correctType);
            Paper.mergeQues =
              state.info.cardInfo.mergeQues == null ? true : state.info.cardInfo.mergeQues;
            Paper.ansToTop = state.info.cardInfo.ansToTop;
            if(Paper.ansToTop){
              Paper.ansToTopConfig.object = true;
              Paper.ansToTopConfig.fill = true;
            }else{
              Paper.ansToTopConfig = state.info.cardInfo.ansToTopConfig || Paper.ansToTopConfig;
            }
            Paper.ansType = state.info.cardInfo.ansType || ANSWER_TYPE.fill;
            Paper.numberLayout = state.info.cardInfo.numberLayout || INUMBERLAYOUT.TICKET;
          }

          if (!isNullOrUndefined(result.data.cardQueInfo)) {
            const cardQues = JSON.parse(result.data.cardQueInfo);
            let quesList = {};
            for (let key in cardQues) {
              //兼容复制教辅题目追加new导致无法匹配问题
              const newKey = key+'new';
              if(result.data.quesInfo.includes(newKey)){
                cardQues[key].id = newKey;
                quesList[newKey] = cardQues[key]
              }else{
                quesList[key] = cardQues[key]
              }
            }
            state.info.cardQueInfo = quesList;
          }
          if (result.data.groupImgData) {
            JSON.parse(result.data.groupImgData).forEach(param => {
              Paper.groupImgData.set(param[0], param[1]);
            });
            Paper.pageType = Paper.groupImgData.size > 0 ? 1 : 0;
          }
          let _quesInfo = result.data.quesInfo ? JSON.parse(result.data.quesInfo) : [];
          let { list, ids } = Paper.convertQuesToFlat(_quesInfo);
          if (Paper.cardType == ICARDMODEL.BLANKCARD || Paper.cardType == ICARDMODEL.PHOTO) {
            state.info.quesInfo = Paper.updateAndMergeQuestions(list);
          } else {
            state.info.quesInfo = Paper.updateQuestions(list);
          }

          quesIds = ids;
          Paper.isEdit = result.data.workNum > 0 || result.data.isOverCard == 1;
          Paper.stuNoLength = Number(result.data.stuNoLength) || Paper.stuNoLength || Paper.stuNoList[0];
          Paper.isRelatedWork = result.data.isRelatedWork > 0;
        } else {
          alert('题卡加载失败：' + result.msg);
        }
      } else {
        const result: any = await getWorJsonByWork({
          workId: props.id,
          schoolId: state.info.schoolId,
        });
        if (result && result.code === 1) {
          state.info.quesInfo = JSON.parse(result.data);
          state.idKey = 'questionId';
          state.info.quesInfo.forEach((bigQues: any) => {
            // 包含parentId，改题型为混合题，这里不特殊处理
            if (bigQues.parentId) return;

            bigQues.smallQueList.forEach((ques: any) => {
              quesIds.push(ques[state.idKey]);
            });
            bigQues.data = bigQues.smallQueList;
            bigQues.name = bigQues.title;
          });
        }
      }
      Paper.name = state.info.name;
      if (state.info.nameHtml) {
        Paper.nameHtml = state.info.nameHtml;
      } else {
        Paper.nameHtml.html = state.info.name;
      }
      Paper.subjectId =
        state.info.subjectId ||
        getQueryString('subjectId') ||
        (Paper.subjectList.length ? Paper.subjectList[0].subjectId : '');
      // if (Paper.subjectId == '3' || Paper.subjectId == '12') {
      //   Paper.fontFamily = 'Times New Roman';
      // }
      Paper.setHasLineBySubject(state.info.subjectId);
      Paper.setIsCanFillEvaBySubject(state.info.subjectId);

      Paper.gradeId = state.info.gradeId;
      // Paper.userId = state.info.userId;
      Paper.schoolId = state.info.schoolId;
      // 纯答题卡
      if (Paper.cardType == ICARDMODEL.BLANKCARD || Paper.cardType == ICARDMODEL.PHOTO ) {
        // Paper.updateAndMergeQuestions(deepClone(state.info.quesInfo || []))
        Paper.items = deepClone(state.info.items || []);
        Paper.questions = deepClone(state.info.cardQueInfo || state.quesMap || []);
      } else if (Paper.cardType == ICARDMODEL.QUESCARD || Paper.cardType == ICARDMODEL.ONLYCARD) {
        await findQuesInfo(quesIds);
      }
    };

    /**
     * @name 根据试卷id获取试卷信息
     */
    const findPaperInfo = async () => {
      const result: any = await getExamPaperInfo({
        schoolId: state.info.schoolId,
        sourceId: props.id,
      });
      if (result && result.code === 1) {
        Paper.paperId = result.data.scId;
        Paper.stuNoList = result.data.numberLengthList.length
          ? result.data.numberLengthList
          : [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14];
        Paper.stuNoLength =
          Number(getQueryString('stuNoLength')) || `${result.data.maxNumber}`.length;
      }
    };

    const getTestBank = async (id: string) => {
      const res = await getTestBankInfo({ id: id });
      if (res.code == 1) {
        state.info.name = res.data.TB_Name;
        state.info.schoolId = res.data.schoolId;
        state.info.schooldName = res.data.schoolName;
        Paper.headerName = state.info.schooldName;
      }
    };

    const getAIScanSubject = async () => {
      const res = await getAIScanSubjectAPI({
        schoolId: state.info.schoolId,
      })
      if(res.code == 1){
        let subjectIds = res.data
          .filter((item: any) => item.aiCorrect == 1 && item.quesType == 1)
          .map((item: any) => {
            return Number(item.subjectId)
          });
        Paper.aiScanSubjectIds = subjectIds;
      }
    }

    const getSchoolConfig = async () => {
      const res = await getPublicConfigBySchoolInfo({
        schoolId: Paper.schoolId,
        dictCode: '115,117,121,133,141',
        userId: Paper.userId,
      })
      if (res.code == 1) {
        res.data.forEach((dict) => {
          if (dict.dictCode == "115") {
            Paper.isAICorrect = dict.state == '1';
          } else if (dict.dictCode == "117") {
            Paper.isHandWrite = dict.state == '1';
          } else if (dict.dictCode == "121") {
            Paper.isScanFill = dict.state == '1';
          } else if (dict.dictCode == "133") {
            Paper.isMoreNoHeader = dict.state == '1';
          } else if (dict.dictCode == "141") {
            Paper.isHandWriteAns = dict.state == '1';
          }
        })
      }
      state.loading = false;
    }

    const preview = () => {
      state.showPreview = true;
    };
    const downloadEvent = async () => {
      const loading = ElLoading.service({
        lock: true,
        text: '答题卡导出中',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      const res = await examCard({
        paperId: Paper.paperId,
        type: [IPAGELAYOUT.A4,IPAGELAYOUT.A42].includes(Paper.pageLayout) ? 'A4' : 'A3',
        preview: 1,
        pageCount: Paper.pageCount,
      });
      if (res.code == 1) {
        base64toFile(res.data, `【预览版】${Paper.name}.pdf`);
        ElMessage({
          message: '导出成功',
          type: 'success',
        });
      } else {
        ElMessage({
          message: '导出失败',
          type: 'error',
        });
      }
      loading.close();
    };

    let events = [];
    let stopFn;
    let playFn;
    const record = () => {
      return;
      if (state.isProd) return;
      stopFn = rrweb.record({
        emit(event) {
          events.push(event);
        },
        sampling: {
          // 不录制鼠标移动事件
          mousemove: true,
          // 不录制鼠标交互事件
          mouseInteraction: {
            MouseUp: false,
            MouseDown: false,
            Click: true,
            ContextMenu: false,
            DblClick: false,
            Focus: true,
            Blur: false,
            TouchStart: false,
            TouchEnd: false,
          },
          // 设置滚动事件的触发频率
          scroll: 150, // 每 150ms 最多触发一次
          // set the interval of media interaction event
          media: 800,
          // 设置输入事件的录制时机
          input: 'last', // 连续输入时，只录制最终值
        },
        packFn: rrweb.pack,
      });
    };

    const replay = () => {
      stopFn();
      playFn = new rrweb.Replayer(events, { unpackFn: rrweb.unpack });
      playFn.play();
      state.showPlay = true;
    };

    const closePlay = () => {
      playFn.destroy();
      record();
      state.showPlay = false;
    };

    /**
     * @name 重置试卷数据
     */
    const resetPaper = async () => {
      const params = {
        tbId: Paper.tbId,
        optUserId: Paper.userId,
      };
      const res = await clearTestBankQueInfo(params);
      if (res.code == 1) {
        location.reload();
      } else {
        ElMessage({
          message: res.msg,
          type: 'error',
        });
      }
    };

    /**
     * 页面一开始加载
     */
    onMounted(async () => {
      Paper.userId = ssoClient.getUserInfo().id;
      Paper.tbId = props.id;
      Paper.abType = getQueryString('abType');
      if (props.cardType == '0') {
        Paper.cardType = ICARDMODEL.BLANKCARD;
      } else if (props.cardType == '4') {
        Paper.cardType = ICARDMODEL.ENGLISH;
        Paper.fontSize = "3.2mm";
        Paper.space = "10mm";
      } else {
        Paper.cardType = ICARDMODEL.QUESCARD;
      }
      state.cardType = Paper.cardType;
      await getTestBank(props.id);
      await getAIScanSubject();
      await Paper.getSubject(state.info.schoolId);
      await findPaperInfo();
      try {
        await findWorkInfo();
      } catch (e) {
        console.warn(e)
        await ElMessageBox.confirm('题目匹配存在异常，是否尝试重置数据？', '提示', {
          confirmButtonText: '重置',
          cancelButtonText: '取消',
          type: 'warning',
        });
        await resetPaper()
      }

      await getSchoolConfig();
      // (document.body.style as any).zoom = 100 / getBrowerZoom();
      window.addEventListener('resize', function () {
        const zoom = getBrowerZoom();
        if (zoom !== 1) {
          state.isShowTip = true;
        } else {
          state.isShowTip = false;
        }
      });
      record();
    });

    return {
      ...toRefs(state),
      activeCardComponent,
      preview,
      downloadEvent,
      closePlay,
      replay,
    };
  },
});
</script>

<style lang="scss" scoped>
.c30-teach-card-container {
  height: 100%;

  .c30-teach-card-main {
    height: 100%;
    width: 351mm;
    margin: 0 auto;
    border: 0 solid green;
    padding: 0;
    overflow: hidden;

    .content {
      width: 224mm;
      display: inline-block;
      overflow-y: scroll;
      overflow-x: hidden;
      height: 100%;
      padding-top: 6.5mm;
      box-shadow: 0px 3px 12px rgba(0, 0, 0, 0.3);

      .a4 {
        width: 210mm;
        margin: 0 auto;
        background: #fff;
      }

      .a3,
      .a23,
      .a32 {
        width: 220mm;
        margin: 0 auto;
        background: #fff;
      }

      .a33 {
        width: 152mm;
        margin: 0 auto;
        background: #fff;
      }
    }
  }
}

.silder {
  position: relative;
  width: 127mm;
  height: 100%;
  display: inline-block;
  margin: 0;
  padding: 0;
  border-right: 2px solid #d3d3d3;
  background-color: #fff;
  overflow: auto;
}

.close {
  width: 30px;
  height: 30px;
  background-color: #8e8e8e;
  z-index: 999;
  border-radius: 2px;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCI+PGRlZnM+PHN0eWxlLz48L2RlZnM+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTg5LjYgMEwxMDI0IDkzNC40bC04OS42IDg5LjZMMCA4OS42IDg5LjYgMHoiLz48cGF0aCBmaWxsPSIjZmZmIiBkPSJNMTAyNCA4OS42TDg5LjYgMTAyNCAwIDkyOCA5MzQuNCAwbDg5LjYgODkuNnoiLz48L3N2Zz4=);
  background-size: auto 25px;
  background-position: 50% center;
  background-repeat: no-repeat;
  cursor: pointer;
  top: 20px;
  right: 50px;
  position: fixed;
}

.downPreview {
  width: 90px;
  height: 30px;
  background-color: #8e8e8e;
  z-index: 999;
  border-radius: 2px;
  cursor: pointer;
  top: 20px;
  right: 150px;
  position: fixed;
  text-align: center;
  line-height: 30px;
  color: #fff;
}
</style>

<style lang="scss">
.replayer-wrapper {
  position: absolute;
  top: 0;
  z-index: 9999;
}

@media print {
  .page-break {
    page-break-after: always;
  }
}

.uneditstyle {
  pointer-events: none;
}

.dotWrapper {
  width: 100%;
  padding: 0 3mm;
  text-align: left;
  word-break: break-all;
  line-height: 9mm !important;
  font-size: 3.7mm !important;
  font-family: 'Times New Roman', Times, serif !important;

  u {
    font-family: Times New Roman;
    line-height: 11.5mm;
    text-decoration-thickness: 0;
  }

  ::v-deep p {
    line-height: unset;

    &:after {
      display: none;
    }
  }
}

.ques-box .four-line-container {
  text-align: center;
}

.four-line-grid {
  display: inline-block;
  position: relative;
  height: var(--lineHeight);
}

.four-line-grid .four-line {
  position: absolute;
  width: 100%;
  height: 1px;
}

/* 定义每条线的位置 */
.four-line-grid .four-line:nth-child(1) {
  top: 0;
  border-top: 1px solid #707070;
}

.four-line-grid .four-line:nth-child(2) {
  top: 33%;
  border-top: 1px dashed #808080;
}

.four-line-grid .four-line:nth-child(3) {
  top: 66%;
  border-top: 1px dashed #808080;
}

.four-line-grid .four-line:nth-child(4) {
  top: 100%;
  border-top: 1px solid #707070;
}

.tianzi-grid {
  display: inline-block;
  width: var(--lineHeight);
  height: var(--lineHeight);
  position: relative;
  border: 1px solid #707070;
  margin: 1px 1px 0 0;
}

.tianzi-grid::before,
.tianzi-grid::after {
  content: "";
  position: absolute;
}

.tianzi-grid::before {
  left: 50%;
  border-left: 1px dashed #808080;
  width: 1px;
  height: 100%;
}

.tianzi-grid::after {
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  height: 1px;
  border-top: 1px dashed #808080;
}
</style>
