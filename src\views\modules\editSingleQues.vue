<!-- 添加/编辑单大题 -->
<template>
  <div class="c30-ques-add-modal">
    <el-dialog v-model="isShowDialog" :title="`${mode == 'add' ? '增加' : '编辑'} ${baseForm.type} 大题`"
      :close-on-click-modal="false" destroy-on-close :before-close="closeDialog" class="el-dialog--big" draggable>
      <el-form class="wh100" ref="formRef" :model="baseForm" label-width="90px">
        <el-container class="wh100">

          <el-aside class="from-base-col" width="350px">
            <div class="from-base-wrap">

              <el-form-item label="大题名称" prop="name" :rules="rules.name">
                <el-input v-model="baseForm.name"></el-input>
              </el-form-item>

              <el-form-item label="显示名称" v-if="!isPhotoCorrect">
                <el-switch v-model="baseForm.showName" inline-prompt active-text="是" inactive-text="否" active-value="1"
                  inactive-value="0" />
              </el-form-item>

              <el-form-item style="display: inline-flex;" label="隐藏题干" v-if="isAnsToTop">
                <el-switch v-model="baseForm.hideQuesSurface" inline-prompt active-text="是" inactive-text="否"
                  active-value="1" inactive-value="0" />
              </el-form-item>
              <el-form-item style="display: inline-flex;" label="隐藏小问" v-if="isAnsToTop && baseForm.data[0].level == 3">
                <el-switch v-model="baseForm.hideSmallQues" inline-prompt active-text="是" inactive-text="否"
                  active-value="1" inactive-value="0" />
              </el-form-item>

              <el-form-item label="独立成组"
                v-if="Paper.cardType != ICARDMODEL.QUESCARD && isShowOption && !isPhotoCorrect">
                <el-switch title="客观题合并后，可设置该大题独立显示" v-model="baseForm.groupAlone" :disabled="!Paper.mergeQues"
                  inline-prompt active-text="是" inactive-text="否" active-value="1" inactive-value="0" />
              </el-form-item>

              <template v-if="mode == 'add'">
                <el-form-item v-if="Paper.cardType == ICARDMODEL.BLANKCARD || Paper.cardType == ICARDMODEL.PHOTO"
                  label="起止题号">
                  <el-input style="width: 45px;" v-model="baseForm.startNo" @input="checkStartNo(baseForm)" @blur="setQuesNum"></el-input>
                  -
                  <el-input style="width: 45px;" v-model="baseForm.endNo" @input="checkEndNo(baseForm)" @blur="setQuesNum"></el-input>
                  <!-- <el-button style="margin-left: 5px;" type="primary" @click="setQuesNum" circle><el-icon><Check /></el-icon></el-button> -->
                </el-form-item>
              </template>
              <template v-else>
                <el-form-item v-if="Paper.cardType == ICARDMODEL.BLANKCARD || Paper.cardType == ICARDMODEL.PHOTO"
                  label="小题数量">
                  <el-input-number v-model="baseForm.count" :min="1" @change="changeQuesNum" size="small" />
                </el-form-item>
              </template>

              <el-form-item label="每题分数">
                <el-input-number min="0" max="100" :precision="1" v-model="baseForm.quesScore"
                  @change="changeScoreEvent" size="small" />
              </el-form-item>

              <el-form-item label="选项个数" v-if="isShowOption" prop="optionCount" :rules="rules.numberRules">
                <el-select style="width: 140px;" @change="changeOptionEvent" v-model="baseForm.optionCount">
                  <template v-for="idx of Paper.optionCountMax" :key="idx">
                    <el-option :label="idx" :value="idx" v-if="idx > 1">
                    </el-option>
                  </template>
                </el-select>
              </el-form-item>

              <el-form-item label="判分规则" prop="halfScore" :rules="rules.floatNumRules"
                v-if="baseForm.typeId == QUES_TYPE.choice">
                <el-radio-group v-model="baseForm.ruleType" :disabled="isPhotoCorrect" @change="changeRuleTypeEvent">
                  <el-radio :label="IRULERTYPE.STANDAD">标准</el-radio>
                  <el-radio :label="IRULERTYPE.EXAM">新高考</el-radio>
                  <el-radio :label="IRULERTYPE.BREAK">断句题</el-radio>
                  <el-radio :label="IRULERTYPE.CUSTOM">自定义</el-radio>
                </el-radio-group>
                <template v-if="baseForm.ruleType == IRULERTYPE.STANDAD">
                  全对的满分，错选的不得分，少选得 <el-input style="width: 50px" v-model="baseForm.halfScore"
                    @change="changeHalfScoreEvent"></el-input> 分</template>
                <template v-else-if="baseForm.ruleType == IRULERTYPE.EXAM">
                  <span
                    style="white-space: normal;color: darkgrey;">若满分6分，全部选对得6分，有选错的得0分。若只有两个正确选项，每选对一个得3分；若共有3个正确选项，每选对一个得2分。</span>
                </template>
                <template v-else-if="baseForm.ruleType == IRULERTYPE.BREAK">
                  <span style="white-space: normal;color: darkgrey;">每答对一处得1分，填涂超出答案个数不得分。</span>
                </template>
                <template v-else>
                  <span @click="showSetDialog = true" class="set-score-rule"><el-icon>
                      <Setting />
                    </el-icon>特殊规则</span>
                </template>
              </el-form-item>

              <!-- 判断题个性化 start -->
              <el-form-item label="判断题样式" v-if="baseForm.typeId == QUES_TYPE.judge">
                <el-select v-model="baseForm.judgeType">
                  <el-option :label="'√/×'" :value="JUDGE_TYPE.MARK">
                  </el-option>
                  <el-option :label="'T/F'" :value="JUDGE_TYPE.TF"> </el-option>
                  <el-option :label="'A/B'" :value="JUDGE_TYPE.AB"> </el-option>
                </el-select>
              </el-form-item>
              <!-- 判断题个性化 end-->

              <!-- 填空题、简答题个性化 start -->
              <el-form-item label="空格长度"
                v-if="(baseForm.typeId == QUES_TYPE.fill || baseForm.typeId == QUES_TYPE.fillEva) && Paper.cardType != ICARDMODEL.QUESCARD"
                prop="step">
                <el-select v-model="baseForm.lineType" @change="changeLineWidth">
                  <el-option :label="'整行'" :value="LINE_WIDTH.ENTIRE">
                  </el-option>
                  <el-option :label="'1/2行'" :value="LINE_WIDTH.HALF">
                  </el-option>
                  <el-option :label="'1/3行'" :value="LINE_WIDTH.THIRD">
                  </el-option>
                  <el-option :label="'1/4行'" :value="LINE_WIDTH.QUARTER">
                  </el-option>
                  <el-option :label="'1/5行'" :value="LINE_WIDTH.FIFTH">
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 填空题、简答题个性化 end -->

              <!-- 填空题、简答题个性化 start -->

              <template
                v-if="baseForm.scanMode != QUES_SCAN_MODE.AI_FILL && isHandCorrent && (baseForm.typeId == QUES_TYPE.fill || baseForm.typeId == QUES_TYPE.subject)">
                <el-form-item label="打分模式">
                  <el-select @change="changeMakeType" class="mark-type-group" style="width: 60%"
                    v-model="baseForm.markType">
                    <el-option :value="MARK_TYPE.NUMBER" label="分数格"></el-option>
                    <el-option :value="MARK_TYPE.YESORNO" label="对错"></el-option>
                    <el-option v-if="baseForm.typeId == QUES_TYPE.subject && Paper.isHandWrite" :value="MARK_TYPE.WRITE"
                      label="手写"></el-option>
                    <el-option v-if="Paper.isFillQues(baseForm.typeId)" :value="MARK_TYPE.ONLYNO"
                      label="仅标识错误"></el-option>
                    <el-option v-if="Paper.isScanFill && Paper.isFillQues(baseForm.typeId)" :value="MARK_TYPE.ONLINE"
                      label="线上批改"></el-option>
                  </el-select>
                  <el-button type="text" v-if="baseForm.markType == MARK_TYPE.ONLINE" style="margin-left: 5px;"
                    @click="showFillSetDialog = true">设置</el-button>
                </el-form-item>
                <el-form-item label="判分格位置"
                  v-if="isHandCorrent && baseForm.typeId == QUES_TYPE.fill && baseForm.markType != MARK_TYPE.ONLINE">
                  <el-radio-group @change="syncSmallQuesProps(['gridPlace'])" v-model="baseForm.gridPlace">
                    <el-radio-button :label="GRIDPLACE_TYPE.FRONT">题干前</el-radio-button>
                    <el-radio-button :label="GRIDPLACE_TYPE.AFTER">题干后</el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </template>
              <!-- 填空题、简答题个性化 end -->

              <!-- 简答题个性化 start -->
              <template
                v-if="isHandCorrent && baseForm.typeId == QUES_TYPE.subject && (baseForm.markType == MARK_TYPE.NUMBER || baseForm.markType == MARK_TYPE.WRITE)">
                <el-form-item v-if="baseForm.markType == MARK_TYPE.NUMBER" label="十个位分开">
                  <el-switch v-model="baseForm.gridType" inline-prompt active-text="是" inactive-text="否"
                    :active-value="GRID_TYPE.YES" :inactive-value="GRID_TYPE.NO" />
                </el-form-item>
                <el-form-item label="分数精度">
                  <el-radio-group v-model="baseForm.decimal" @change="changeDecimalType">
                    <el-radio-button :label="DECIMAL_TYPE.NOTHAVE">无小数</el-radio-button>
                    <el-radio-button :label="DECIMAL_TYPE.HAVE">有小数</el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </template>

              <el-form-item label="每题步进" v-if="showNameRulesInput" prop="step">
                <el-input @change="checkIsStepRule" v-model="baseForm.step"></el-input>
              </el-form-item>

              <!-- 作文 -->
              <template v-if="baseForm.isWriting">
                <el-form-item label="字数选择">
                  <el-input v-model="baseForm.wordNumber" @change="changeWordNumber"></el-input>
                </el-form-item>
                <el-form-item label="作文格大小">
                  <div class="cell-radio">
                    <el-radio-group @change="changeCellSize" v-model="baseForm.cellSize">
                      <el-radio-button label="small" size="mini">小</el-radio-button>
                      <el-radio-button label="medium" size="mini">中</el-radio-button>
                      <el-radio-button label="big" size="mini">大</el-radio-button>
                    </el-radio-group>
                  </div>
                </el-form-item>
                <el-form-item label="卷面分">
                  <el-switch :model-value="baseForm.hasFaceScore" inline-prompt active-text="有" inactive-text="无"
                    :active-value="true" :inactive-value="false" @change="toggleFaceQues" />
                </el-form-item>
              </template>
              <!-- 简答题个性化 end -->

              <el-form-item label="智能批改" v-if="!Paper.abType && Paper.getIsCanFillEva(baseForm.typeId)">
                <el-switch :model-value="baseForm.scanMode" :active-value="QUES_SCAN_MODE.AI_FILL"
                  :inactive-value="QUES_SCAN_MODE.NORMAL" @change="changeFillType" />
                <el-button type="text" v-if="baseForm.scanMode == QUES_SCAN_MODE.AI_FILL" style="margin-left: 5px;"
                  @click="showFillSetDialog = true">设置</el-button>
                <span v-if="Paper.isChineseSubject()" style="color: #999; font-size: 12px;"> 仅适用于古诗默写等固定答案批改场景</span>
              </el-form-item>

              <el-form-item label="选做题" v-if="isShowChooseDoQuesBtn">
                <el-button type="text" @click="showChooseDoQues = true">设置</el-button>
              </el-form-item>
            </div>
          </el-aside>

          <el-main class="form-table">
            <el-table ref="formTable" row-key="id" table-layout='auto' class="wh100" max-height="530" default-expand-all
              :data="baseForm.data" v-loading="dataLoading">
              <el-table-column prop="quesNos" label="题号" :width="showAddSmallQuesBtn ? 'auto' : 110" align="center">

                <template #default="scope">
                  <el-input v-model="scope.row.quesNos" :disabled="scope.row.isChooseDo"
                    @input="onChangeQuesName(scope.row)" class="sort-input"></el-input>
                </template>
              </el-table-column>

              <el-table-column prop="type" label="题型" align="center">

                <template #default="scope">
                  <span>{{ convertQuesName(scope.row.typeId) }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="lineNum" label="空格数" align="center"
                v-if="isFillQues && Paper.cardType != ICARDMODEL.QUESCARD">

                <template #default="scope">
                  <span v-if="scope.row.children && scope.row.children.length">--</span>
                  <el-select @change="changeLineNum(scope.row)" v-model="scope.row.lineNum" v-else>
                    <el-option v-for="item in 20" :key="item" :label="item" :value="item">
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column prop="optionCount" label="选项个数" align="center" v-if="isShowOption">

                <template #default="scope">
                  <el-select style="width: 100px;" @change="changeOptionCount(scope.row)"
                    v-model="scope.row.optionCount">
                    <template v-for="idx of Paper.optionCountMax" :key="idx">
                      <el-option :label="idx" :value="idx" v-if="idx > 1">
                      </el-option>
                    </template>
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column prop="score" label="分值" align="center">

                <template #default="scope">
                  <el-input v-model="scope.row.score" class="score-input" :disabled="scope.row.isChooseDo"
                    @input="caculTotalScoreChildren(scope.row)" @change="changeQuesScore(scope.row)"></el-input>
                </template>
              </el-table-column>

              <el-table-column fixed="right" label="操作" align="center" v-if="showAddSmallQuesBtn">

                <template #default="scope">
                  <el-button v-if="!scope.row.parentId" :disabled="scope.row.isChooseDo" type="link" size="small"
                    @click="addSmallQues(scope.row)">+
                    增加小题</el-button>
                  <el-button v-else type="link" size="small" @click="deleteSmallQues(scope.row)">删除</el-button>
                </template>
              </el-table-column>

            </el-table>
          </el-main>
        </el-container>

      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button size="large" type="default" @click="closeDialog">
            <slot name="confirmText">取消</slot>
          </el-button>
          <el-button size="large" type="primary" @click="confirmSubmit" :loading="submitLoading"
            style="margin-right: 30px">
            <slot name="cancleText">确定</slot>
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <set-choice-scores :isShowDialog="showSetDialog" :item="baseForm.data" v-if="showSetDialog"
    @close-dialog="showSetDialog = false" @update-score-rules="updateRules"></set-choice-scores>
  <set-fill-scores :isShowDialog="showFillSetDialog" :item="baseForm.data" v-if="showFillSetDialog"
    @close-dialog="showFillSetDialog = false" @update-score-rules="updateRules"></set-fill-scores>
  <set-choose-do-ques :isShowDialog="showChooseDoQues" :item="baseForm.data" :doQuesList="baseForm.doQuesList"
    v-if="showChooseDoQues" @close-dialog="showChooseDoQues = false" @update-score-rules="updateRules"
    @update-choosedo-ques="updateChooseDo"></set-choose-do-ques>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, nextTick, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import {
  QUES_TYPE,
  JUDGE_TYPE,
  GRID_TYPE,
  GRIDPLACE_TYPE,
  MARK_TYPE,
  DECIMAL_TYPE,
  IBigQues,
  ISmallQues,
  LINE_WIDTH,
  ICorrectType,
  ICARDMODEL,
  SUBJECT_MODE,
  IRULERTYPE,
  QUES_SCAN_MODE
} from '@/typings/card';
import {
  checkFloatNum,
  checkIsNumber,
  toDecimal,
  generateUUID,
  arabToChinese,
  deepClone,
  sleep,
} from '@/utils/util';
import { getTypeName } from '@/utils/question';
import Paper from '../paper';
import { PaperConstant } from '../paper.constant';
import setChoiceScores from './setChoiceScores.vue';
import setFillScores from './setFillScores.vue';
import setChooseDoQues from './setChooseDoQues.vue';
export default defineComponent({
  props: {
    //弹框是否显示
    isShowDialog: {
      type: Boolean,
      default: false,
    },
    // 是否作文
    isWriting: {
      type: Number,
      default: false,
    },
    //题型
    quesType: {
      type: String,
      default: '',
    },
    //最后一题下标
    index: {
      type: Number,
      default: 0,
    },
    mode: {
      type: String,
      default: 'add',
    },
  },
  components: {
    setChoiceScores,
    setFillScores,
    setChooseDoQues
  },
  emits: ['close-dialog', 'update-question', 'update-mixin-question'],
  setup(props: any, ctx: any) {
    //表单对象
    const formRef = ref<any>(null);
    // 表格表单对象
    const formTable = ref<any>(null);

    // 填空题和简答题支持成为混合模式，支持增加小题
    const canBeMixined =
      Paper.isFillQues(props.quesType) || (props.quesType == QUES_TYPE.subject && !props.isWriting);

    const state = reactive({
      Paper: Paper,
      // 题目类型
      QUES_TYPE: QUES_TYPE,
      // 判断题样式类型
      JUDGE_TYPE: JUDGE_TYPE,
      // 判分格位置类别
      GRIDPLACE_TYPE: GRIDPLACE_TYPE,
      // 打分类型
      MARK_TYPE: MARK_TYPE,
      // 十个位是否分开
      GRID_TYPE: GRID_TYPE,
      // 分数精度类别
      DECIMAL_TYPE: DECIMAL_TYPE,
      LINE_WIDTH: LINE_WIDTH,
      // 批阅类型
      ICorrectType: ICorrectType,
      // 制卡模式
      ICARDMODEL: ICARDMODEL,
      IRULERTYPE: IRULERTYPE,
      QUES_SCAN_MODE: QUES_SCAN_MODE,
      isShowView: false,
      // 提交按钮加载中状态
      submitLoading: false,
      // 数据加载状态
      dataLoading: true,
      // 表单模型
      baseForm: {
        quesNos: '',
        isChangeSort: true,
        name: '',
        type: '',
        isAverage: false,
        isChange: false,
        showName: '1',
        // 是否隐藏题面
        hideQuesSurface: '0',
        // 是否隐藏小问
        hideSmallQues: '0',
        quesScore: 5,
        typeId: '',
        score: 5,
        count: 1,
        startNo: 1,
        endNo: "",
        optionCount: 4,
        arrange: 1,
        halfScore: 2.5,
        ruleType: IRULERTYPE.STANDAD,
        rules: [],
        mixinMode: false,
        data: [],
        children: [],
        lineType: Paper.lineType,
        groupAlone: '0',
        // 是否有卷面分
        hasFaceScore: false,
        decimal: DECIMAL_TYPE.NOTHAVE,
      } as any,
      samllModel: {
        quesNos: '',
        score: 5,
        name: '',
        typeId: '',
        id: '',
        type: '',
        isChange: false,
        answer: '',
        optionCount: 4,
        decimal: DECIMAL_TYPE.NOTHAVE,
      } as any,
      // 表单验证规则
      rules: {
        name: [
          {
            required: true,
            message: '必填项',
            trigger: 'blur',
          },
        ],
        // 数字和必填项验证规则
        numberRules: [
          {
            required: true,
            message: '必填项',
            trigger: 'blur',
          },
          { validator: checkIsNumber, trigger: 'blur' },
        ],
        // 数字 可以是小数
        floatNumRules: [
          {
            required: true,
            message: '必填项',
            trigger: 'blur',
          },
          {
            validator: checkFloatNum,
            trigger: 'blur',
          },
        ],
      },
      addQuesList: PaperConstant.QuesTypeList,
      //已有的小题最后一个题号
      lastSmallQuesIndex: 0,
      // 显示增加小题按钮
      showAddSmallQuesBtn: Paper.cardType == ICARDMODEL.BLANKCARD && canBeMixined,
      // 是否显示选项
      isShowOption: Paper.isChoiceQues(props.quesType),
      // 大题编辑前长度
      quesLengthBeforeEdit: 0,
      // 是否手阅
      isHandCorrent: Paper.correctType == ICorrectType.HAND,
      // 是否填空题型
      isFillQues: Paper.isFillQues(props.quesType),
      // 是否英语作文题
      isEnglishWriting: props.isWriting && Paper.isEnglishSubject(),
      showSetDialog: false,
      showFillSetDialog: false,
      showChooseDoQues: false
    });

    // 检查是否符合步进规则
    const checkIsStepRule = (value: string) => {
      // if (value.includes('.')) {
      //   let numbers = value.split('.');
      //   if (Number(numbers[1]) !== 5) {
      //     state.baseForm.step = `${numbers[0]}.5`;
      //   }
      // }
      const valueNum = Number(value) || 1;
      if (state.baseForm.decimal == DECIMAL_TYPE.NOTHAVE) {
        state.baseForm.step = valueNum.toFixed(1);
      } else {
        state.baseForm.step = Number(valueNum.toFixed(0)) || 1;
      }

      syncSmallQuesProps(['step']);
    };

    const isAnsToTop = computed(() => {
      return ([QUES_TYPE.choice, QUES_TYPE.judge, QUES_TYPE.singleChoice].includes(Number(state.baseForm.typeId)) && Paper.ansToTopConfig.object) ||
        ([QUES_TYPE.fill, QUES_TYPE.fillEva].includes(Number(state.baseForm.typeId)) && Paper.ansToTopConfig.fill)
    })

    // 显示每题步进
    const showNameRulesInput = computed(() => {
      return (
        state.isHandCorrent &&
        state.baseForm.markType == MARK_TYPE.NUMBER &&
        ((state.baseForm.typeId == QUES_TYPE.subject && state.baseForm.gridType == GRID_TYPE.NO) ||
          Paper.isFillQues(state.baseForm.typeId))
      );
    });

    // 是否拍改类型
    const isPhotoCorrect = computed(() => {
      return Paper.correctType == ICorrectType.PHOTO;
    })


    const isShowChooseDoQuesBtn = computed(() => {
      return state.baseForm.typeId == QUES_TYPE.subject;
    });

    state.baseForm.showType = 'vertical';
    state.baseForm.verticalLines = 5;
    /* 检测是否所有小问被清空 */
    const checkAllSmallQuesCleared = (): boolean => {
      let len = 0;
      state.baseForm.data.forEach(item => {
        if (!item.children || !item.children.length) return;

        len++;
      });

      return !len;
    };

    const convertQuesName = typeId => {
      if (!typeId) return '';
      return getTypeName(Number(typeId));
    };

    /* 获得小题目模型 */
    const getSmallQuesModel = (row?: any): any => {
      let quesType: QUES_TYPE = state.baseForm.typeId;
      let samllModel: any = {
        quesNos: state.lastSmallQuesIndex + 1,
        score: state.baseForm.quesScore,
        name: state.baseForm.type,
        typeId: quesType,
        id: generateUUID(),
        type: state.baseForm.type,
        isChange: false,
        answer: '',
        halfScore: state.baseForm.halfScore,
        markType: state.baseForm.markType || Paper.markType,
        isSplited: quesType == QUES_TYPE.subject,
      };

      if (Paper.isFillQues(quesType)) {
        samllModel.step = Paper.step;
        samllModel.gridPlace = Paper.gridPlace;
        samllModel.arrange = Paper.arrange;
        samllModel.lineType = state.baseForm.lineType;
        samllModel.lineNum = row ? row.lineNum : Paper.lineNum;
        samllModel.scanMode = state.baseForm.scanMode;
        let line = Paper.createLineModel('50mm', 0);
        line.score = samllModel.score;
        samllModel.lineList = row ? row.lineList : [line];
      } else if (quesType == QUES_TYPE.subject) {
        samllModel.step = Paper.step;
        samllModel.gridPlace = Paper.gridPlace;
        samllModel.arrange = Paper.arrange;
        samllModel.lineType = state.baseForm.lineType;
        samllModel.decimal = state.baseForm.decimal;

        samllModel.isShowSetting = false;
        samllModel.hasLine = Paper.hasLine;
      } else if (Paper.isChoiceQues(quesType)) {
        samllModel.optionCount = state.baseForm.optionCount;
        samllModel.ruleType = state.baseForm.ruleType || IRULERTYPE.STANDAD;
      }

      if (state.showAddSmallQuesBtn) {
        samllModel.children = [];
      }

      return samllModel;
    };

    const getQuesName = () => {
      return state.addQuesList.filter(item => {
        if (item.type == QUES_TYPE.subject) {
          if (!props.isWriting) return item.name;

          return item.isWriting ? item.name : false;
        } else {
          return item.type == props.quesType;
        }
      })[0].name;
    };

    /**
     * @name:处理小题数据
     */
    const handleSmallQues = () => {
      //初始化题目题卡数据
      //选择题
      if (Paper.isChoiceQues(state.baseForm.typeId)) {
        state.baseForm.arrange = Paper.arrange;
      } else if (state.baseForm.typeId == QUES_TYPE.choice) {
        //多选题
        state.baseForm.halfScore = toDecimal(state.samllModel.score / 2);
        state.baseForm.ruleType = IRULERTYPE.STANDAD;
        state.baseForm.arrange = Paper.arrange;
      } else if (state.baseForm.typeId == QUES_TYPE.judge) {
        //判断题
        state.baseForm.judgeType = Paper.judgeType;
        state.baseForm.markType = MARK_TYPE.YESORNO;
      } else if (Paper.isFillQues(state.baseForm.typeId)) {
        //填空题
        state.baseForm.step = Paper.step;
        state.baseForm.markType = Paper.markType;
        state.baseForm.gridPlace = Paper.gridPlace;
        state.baseForm.arrange = Paper.arrange;
        state.baseForm.decimal = Paper.decimal;
        state.baseForm.fillDistance = 10;
        // state.baseForm.lineType = Paper.lineType;

        state.samllModel.isShowSetting = false;
        state.samllModel.lineType = Paper.lineType;
        state.samllModel.lineNum = Paper.lineNum;
      } else if (state.baseForm.typeId == QUES_TYPE.subject) {
        //简答题
        state.baseForm.step = Paper.step;
        state.baseForm.markType = Paper.markType;
        state.baseForm.gridType = Paper.gridType;
        state.baseForm.decimal = Paper.decimal;
        state.baseForm.hasLine = Paper.hasLine;
        state.baseForm.wordNumber = Paper.wordNumber;
        state.baseForm.isWriting = state.isEnglishWriting ? false : props.isWriting;
        state.baseForm.cellSize = Paper.cellSize;
        state.baseForm.rowNum = Paper.rowNum;
        state.baseForm.cells = Paper.cells;

        state.samllModel.hasLine = Paper.hasLine;
        state.samllModel.rowNum = Paper.rowNum;
        state.samllModel.cellSize = Paper.cellSize;
      }
    };

    /**
     * @name:关闭弹窗
     */
    const closeDialog = () => {
      state.submitLoading = false;
      ctx.emit('close-dialog');
      // state.baseForm = {};
    };

    /* 提交混合模式题型 */
    const submitMixinQues = () => {
      // 提交数据之前，清理children对象，计算小题分值
      let quesFlatList = [];
      let score = 0;
      state.baseForm.data.forEach(item => {
        if (!item.parentId) {
          score += Number(item.score);
        }
        quesFlatList.push(item);

        if (item.children && item.children.length) {
          item.mixinMode = true;
          quesFlatList.push(...item.children);
        } else {
          delete item.children;
          if (!item.data) item.data = [deepClone(item)];
        }

        delete item.children;
      });

      state.baseForm.score = score;
      state.baseForm.quesScore = '';
      let updateIndex = 0;
      // 清理混合模式冗余的data
      state.baseForm.data = quesFlatList;
      let quesDatas = deepClone([state.baseForm]);

      delete quesDatas.children;
      if (props.mode == 'add') {
        // 添加混合模式题目
        updateIndex = Paper.quesInfos.length;
        Paper.spliceQuestions(updateIndex, 0, quesDatas);
      } else {
        // 编辑模式
        updateIndex = props.index;
        Paper.spliceQuestions(updateIndex, 1, quesDatas);
      }
      ctx.emit('update-mixin-question', deepClone(state.baseForm), updateIndex, props.mode);
    };

    /* 提交独立大题 */
    const submitSingleQues = () => {
      let qIndex = props.index;
      if (props.mode === 'add') qIndex = Paper.quesInfos.length;

      if (state.baseForm.isWriting) {
        // 作文题在最大分数或者最后一个作文格使用自定义作文格数
        let quesList = state.baseForm.data;
        let maxScore = 0,
          maxScoreItem = null;
        for (let i = 0; i < quesList.length; i++) {
          const item = quesList[i];
          let isLastQues =
            (state.baseForm.hasFaceScore && i === quesList.length - 2) ||
            (!state.baseForm.hasFaceScore && i === quesList.length - 1);

          if (item.score >= maxScore) {
            maxScore = item.score;
            maxScoreItem = item;

            if (isLastQues) break;
          }
        }

        maxScoreItem.wordNumber = state.baseForm.wordNumber;
        delete state.baseForm.wordNumber;
      }
      ctx.emit('update-question', deepClone(state.baseForm), qIndex, props.mode);
    };

    /**
     * @name:确定添加题目
     */
    const confirmSubmit = () => {
      // console.log('state.baseForm.data:', deepClone(state.baseForm.data));

      if (state.baseForm.ruleType == IRULERTYPE.CUSTOM && !state.baseForm.data[0].rules) {
        ElMessage({
          message: '自定义规则未设置',
          type: 'error',
        });
        return;
      }

      state.submitLoading = false;
      formRef.value.validate(async (valid: any, message: string) => {
        if (valid) {
          //更新父页面题目数据
          if (state.baseForm.mixinMode) {
            submitMixinQues();
          } else {
            submitSingleQues();
          }
          closeDialog();
        } else {
          ElMessage({
            message: message,
            type: 'error',
          });
        }
      });
    };

    /**
     * @name: 修改选项个数改变每小题选项个数
     */
    const changeOptionEvent = () => {
      if (Number(state.baseForm.optionCount) > Paper.optionCountMax) {
        state.baseForm.optionCount = Paper.optionCountMax;
      }
      state.baseForm.optionCount = Number(state.baseForm.optionCount);
      state.baseForm.data.forEach((item: ISmallQues) => {
        item.optionCount = state.baseForm.optionCount;
      });
      resetHalfScoreRules();
    };

    /**
     * @name: 初始化题目数据
     * @param index
     */
    const indexMethod = (index: number) => {
      return index + 1;
    };

    const changeScore = () => {
      state.baseForm.quesScore = '';
    };

    const changeQuesScore = (row) => {
      if (row.children && row.children.length) {
        row.children.forEach((rowItem: any) => {
          rowItem.lineList?.forEach(item => {
            item.score = rowItem.score;
          });
        });
      }else{
        row.lineList?.forEach(item => {
          item.score = row.score;
        });
      }
    }
    /**
     * @name: 修改每题分数改变每小题分数
     */
    const changeScoreEvent = () => {
      state.baseForm.quesScore = Number(state.baseForm.quesScore);
      if (state.baseForm.doQuesList && state.baseForm.doQuesList.length) {
        state.baseForm.doQuesList.forEach(doItem => {
          doItem.score = state.baseForm.quesScore
        })
      }
      state.baseForm.data.forEach((item: ISmallQues) => {
        item.score = Number(state.baseForm.quesScore);
        item.halfScore = item.score / 2;
        caculTotalScoreChildren(item);
      });
      resetHalfScoreRules();
    };
    /**
     * @name: 修改半对分值
     */
    const changeHalfScoreEvent = () => {
      state.baseForm.halfScore = Number(state.baseForm.halfScore);
      state.baseForm.data.forEach((item: ISmallQues) => {
        item.halfScore = Number(state.baseForm.halfScore);
      });
      resetHalfScoreRules();
    };

    /**
     * @name: 重置半对判分规则
     */
    const resetHalfScoreRules = () => {
      if (state.baseForm.ruleType != IRULERTYPE.CUSTOM) return;
      state.baseForm.ruleType = IRULERTYPE.STANDAD;
      delete state.baseForm.rules;
      state.baseForm.data.forEach((item: ISmallQues) => {
        item.ruleType = IRULERTYPE.STANDAD;
        //修改半对分值 则自定义分值规则失效
        delete item.rules;
      });
    };

    /**
     * @name: 修改半对判分规则
     */
    const changeRuleTypeEvent = () => {
      state.baseForm.ruleType = Number(state.baseForm.ruleType);
      delete state.baseForm.rules;
      state.baseForm.data.forEach((item: ISmallQues) => {
        item.ruleType = state.baseForm.ruleType;
        delete item.rules;
      });
    };

    /* 获取卷面分文字模板 */
    const getFaceScoreTextTpl = (quesNos: number, score: number) => {
      return `<div class="subject-para-p"><span class="ques-sort">${quesNos}</span><span>.</span><span class="ques-score">(<span class="item-score">${score}</span>分)</span></div><div class="subject-para-p">卷面分无须作答！<br></div>`;
    };

    /* 清除选做题字段数据*/
    const clearChooseData = (item) => {
      delete item.isChooseDo
      delete item.chooseIds
      delete item.chooseName
      delete item.targetIds
      delete item.doCount
      return item
    }

    /* 新增一些小题结构 */
    const addSomeQues = (addLength: number) => {
      let lastQues = state.baseForm.data.at(-1);
      let createSamallQues = [];
      for (let i = 0; i < addLength; i++) {
        let samllModel = clearChooseData(deepClone(lastQues));
        samllModel.editContent = '';
        samllModel.mixinMode = false;
        samllModel.lineList = [];
        const line = Paper.createLineModel('50mm', 0);
        line.score = samllModel.score;
        samllModel.lineList.push(line);
        samllModel.rendered = false;
        samllModel.lineNum = 1;
        samllModel.id = generateUUID();
        samllModel.score = state.baseForm.quesScore || 5;
        samllModel.quesNos = Number(lastQues.quesNos) + i + 1;
        samllModel.quesName = samllModel.quesNos;
        samllModel.decimal = state.baseForm.decimal;
        if (state.showAddSmallQuesBtn && !checkAllSmallQuesCleared()) {
          samllModel.children = [];
        }
        state.samllModel = deepClone(samllModel);
        createSamallQues.push(samllModel);
      }

      return createSamallQues;
    };

    // 通用的分数格式化方法
    const formatNumer = (number: string | number): string => {
      return String(number).replace(/[^\d]/g, '');
    }

    const checkStartNo = (form) => {
      if (form.startNo == '') return;
      form.startNo = formatNumer(form.startNo);
    }

    const checkEndNo = (form) => {
      if (form.endNo == '') return;
      form.endNo = formatNumer(form.endNo);
    }

    const setQuesNum = () => {
      if(state.baseForm.startNo == '' || state.baseForm.endNo == '') return;
      if(state.baseForm.startNo > state.baseForm.endNo) return;
      state.baseForm.count = Number(state.baseForm.endNo) - Number(state.baseForm.startNo) + 1;
      state.baseForm.data.forEach((item, index) => {
        item.quesNos = Number(state.baseForm.startNo) + index;
        item.quesName = item.quesNos;
      });
      changeQuesNum();
    }

    /**
     * @name: 修改小题数量
     */
    const changeQuesNum = async () => {
      state.dataLoading = true;
      await sleep(0);

      let lastLength = state.baseForm.data.length;
      let needCreatLength = state.baseForm.count - lastLength;
      if (needCreatLength == 0) return;

      let faceQues = null;
      if (state.baseForm.hasFaceScore) {
        // 有卷面分，保持卷面分始终在最后一位
        faceQues = state.baseForm.data.at(-1);
        state.baseForm.data = state.baseForm.data.splice(0, state.baseForm.data.length - 1);
      }

      if (needCreatLength > 0) {
        // 增加，复制上一个题目的参数
        state.baseForm.data = state.baseForm.data.concat(addSomeQues(needCreatLength));
        if (faceQues) {
          faceQues.quesNos += needCreatLength;
        }
        resetHalfScoreRules();
      } else {
        // 减少
        state.baseForm.data = state.baseForm.data.splice(0, state.baseForm.count);
        if (faceQues) {
          faceQues.quesNos = Number(state.baseForm.data.at(-1).quesNos) + 1;
        }
      }

      if (faceQues) {
        faceQues.editContent = getFaceScoreTextTpl(faceQues.quesNos, faceQues.score);
        state.baseForm.data.push(faceQues);
      }

      await nextTick();
      state.dataLoading = false;
    };

    /* 计算小题总分 */
    const caculSmallQuesScore = (parentData: any) => {
      if (!parentData) return;
      if (!parentData.children || !parentData.children.length) return;

      if (parentData.children.length > 1) {
        let totalScore = parentData.children.reduce((prev, curr) => {
          const currScore = Number(curr.score) * 10;
          const preScore = Number(
            typeof prev == 'number' || typeof prev == 'string' ? prev : prev.score
          );

          return Number(preScore) + currScore;
        }, 0);
        parentData.score = totalScore / 10;
      } else {
        parentData.score = Number(parentData.children[0].score);
      }
    };

    /* 处理小题小问模型 */
    const handleSmallQuesModel = (item: any) => {
      if (Paper.isFillQues(state.baseForm.typeId)) {
        item.step = state.baseForm.step || Paper.step;
        item.markType = state.baseForm.markType || MARK_TYPE.NUMBER;
        item.gridPlace = state.baseForm.gridPlace || Paper.gridPlace;
        item.arrange = state.baseForm.arrange || Paper.arrange;
        item.lineType = state.baseForm.lineType || Paper.lineType;
        item.decimal = state.baseForm.decimal || Paper.decimal;

        item.isShowSetting = false;
      }
    };

    /* 增加小题小问 */
    const addSmallQues = async (row: any) => {
      state.baseForm.mixinMode = true;

      if (!row.children || !row.children.length) {
        state.baseForm.id = generateUUID();
        row.children = [];
        // 这里需要更新题目id，防止dom已经渲染绑定
        row.id = generateUUID();
      }
      let _small = getSmallQuesModel(row);
      _small.id = generateUUID();

      _small.parentId = row.id;
      row.children.push(_small);
      _small.quesNos = `${row.quesNos}(${row.children.length})`;
      _small.quesName = _small.quesNos;
      handleSmallQuesModel(_small);
      caculSmallQuesScore(row);

      if (row.children.length == 1) {
        addSmallQues(row);
      }
    };

    /* 删除小题小问 */
    const deleteSmallQues = (row: any) => {
      let parentData = state.baseForm.data.filter(item => item.id == row.parentId)[0];
      let index = parentData.children.findIndex(item => item.id == row.id);

      parentData.children.splice(index, 1);
      if (parentData.children.length <= 1) {
        delete parentData.mixinMode;
        delete parentData.children;
      }

      let isCleared = checkAllSmallQuesCleared();
      if (isCleared) {
        // 所有小题小问被清空，设置为普通大题模式
        state.baseForm.mixinMode = false;
        state.baseForm.data.forEach(item => (item.mixinMode = false));
      } else {
        // 有小问时，设置为混合题模式
        state.baseForm.mixinMode = true;
      }

      caculSmallQuesScore(parentData);
    };

    /* 设置小问分值 */
    const caculTotalScoreChildren = (row: any) => {
      let score = row.score;
      score = String(score).replace(/[^\d.]/g, '').replace(/\.{2,}/g, '');
      let parts = score.split('.');
      if (parts.length > 1) {
        parts = parts.splice(0, 2);
        // 如果有小数点
        parts[1] = parts[1].slice(0, 1); // 仅保留一位小数
      }
      score = parts.join('.');
      if (score > 100) {
        score = 100
      }
      row.score = score;
      if (row.children && row.children.length) {
        // 平均修改小题分数
        row.children.forEach((rowItem: any) => {
          rowItem.score = Math.round(row.score / row.children.length);
          if (rowItem.typeMode == SUBJECT_MODE.face) {
            rowItem.editContent = getFaceScoreTextTpl(rowItem.quesNos, rowItem.score);
          }
          rowItem.lineList?.forEach((line: any) => {
            line.score = rowItem.score;
          });
        });
      } else {
        row.lineList?.forEach((line: any) => {
          line.score = row.score;
        });
        let parentData = state.baseForm.data.filter((item: any) => item.id == row.parentId)[0];

        if (parentData) {
          caculSmallQuesScore(parentData);
        } else {
          if (row.typeMode == SUBJECT_MODE.face) {
            row.editContent = getFaceScoreTextTpl(row.quesNos, row.score);
          }
        }
      }
      resetHalfScoreRules();
    };

    /* 修改填空题空格数 */
    const changeLineNum = (row: any) => {
      row.rendered = false;
      row.lineList = [];
      for (let i = 0; i < row.lineNum; i++) {
        const line = Paper.createLineModel('50mm', i);
        if (row.lineNum == 1) {
          line.score = row.score;
        }
        row.lineList.push(line);
      }
    };

    /* 修改选择题选项个数 */
    const changeOptionCount = (row: any) => {
      // 每次修改清空线条信息，以便触发重新计算
      resetHalfScoreRules();
    };

    /**
     * @description: 修改填空题类型
     */
    const changeFillType = (val: string) => {
      if (state.baseForm.scanMode == val) return;

      state.baseForm.scanMode = val;
      const dataList = state.baseForm.data;
      if (!dataList.length) return;

      state.baseForm.data.forEach(ques => {
        ques.scanMode = val;
        ques.children && ques.children.forEach(sq => {
          sq.scanMode = val;
        })
      })

      if (state.baseForm.scanMode == QUES_SCAN_MODE.AI_FILL) {
        state.baseForm.markType = MARK_TYPE.ONLINE;
        state.showFillSetDialog = true;
      }

      // const samallQues = [];
      // // 遍历小题，修改填空题类型并重置配置
      // for (let i = 0; i < dataList.length; i++) {
      //   const item = dataList[i];
      //   const samllModel = getSmallQuesModel();
      //   if (Paper.cardType != ICARDMODEL.BLANKCARD) {
      //     samllModel.id = item.id;
      //     samllModel.quesNo = item.quesNo;
      //   }
      //   if (state.baseForm.scanMode == QUES_SCAN_MODE.AI_FILL) {
      //     samllModel.answer = item.answer.replace(/<[^>]+>(.*?)<\/[^>]+>/, '$1').trim();
      //   }
      //   samllModel.quesNos = item.quesNos;
      //   if (state.showAddSmallQuesBtn && !checkAllSmallQuesCleared()) {
      //     samllModel.children = [];
      //   }
      //   state.samllModel = deepClone(samllModel);
      //   // handleSmallQues();
      //   handleSmallQuesModel(samllModel);
      //   samallQues.push(samllModel);
      // }
      // state.baseForm.data = samallQues;
    };

    /* 切换卷面分 */
    const toggleFaceQues = (show: boolean) => {
      if (state.baseForm.hasFaceScore == show) return;

      state.baseForm.hasFaceScore = show;
      if (show) {
        // 添加卷面分题型
        let lastQues = state.baseForm.data.at(-1);
        let samllModel = deepClone(lastQues);
        samllModel.id = generateUUID();
        samllModel.quesNos = Number(lastQues.quesNos) + 1;
        samllModel.typeMode = SUBJECT_MODE.face;
        samllModel.name = '卷面分';
        samllModel.type = '卷面分';
        samllModel.score = 5;
        samllModel.hasLine = false;
        samllModel.height = '18mm';
        samllModel.editContent = getFaceScoreTextTpl(samllModel.quesNos, samllModel.score);
        if (state.showAddSmallQuesBtn && !checkAllSmallQuesCleared()) {
          samllModel.children = [];
        }
        state.samllModel = deepClone(samllModel);
        state.baseForm.data.push(samllModel);
        state.baseForm.count++;
      } else {
        // 删除卷面分题型
        state.baseForm.data = state.baseForm.data.splice(0, state.baseForm.data.length - 1);
        state.baseForm.count--;
      }
    };

    /* 改变小标题 */
    const onChangeQuesName = (row: any) => {
      row.quesName = row.quesNos;
      if (!row.children || !row.children.length) return;

      row.children.forEach((rowItem: any) => {
        rowItem.quesNos = rowItem.quesNos.replace(
          /.+?(?=[\(\（])|(^|[^0-9])(?=[\(\（])/g,
          row.quesNos
        );
        rowItem.quesName = rowItem.quesNos;
      });
    };

    const updateRules = data => {
      state.baseForm.data = data;
    };

    const updateChooseDo = data => {
      state.baseForm.doQuesList = data;
    }

    /**
     * @description: 改变线条长度
     * @return {*}
     */
    const changeLineWidth = () => {
      state.baseForm.data.forEach(row => {
        row.rendered = false;

        if (row.children) {
          row.children.forEach(subRow => (subRow.rendered = false));
        }
      });
      syncSmallQuesProps(['lineType']);
    };

    /* 同步小题的属性 */
    const syncSmallQuesProps = (pKeys: any[]) => {
      function setObjValue(item: any, pKeys: any[]) {
        pKeys.forEach(pKey => {
          if (typeof pKey === 'string') {
            item[pKey] = state.baseForm[pKey];
          } else {
            item[pKey[0]] = pKey[1];
          }
        });
      }

      state.baseForm.data.forEach((item: ISmallQues) => {
        setObjValue(item, pKeys);
        if (!item.children) return;

        item.children.forEach((subItem: ISmallQues) => setObjValue(subItem, pKeys));
      });
    };

    const changeDecimalType = () => {
      checkIsStepRule(state.baseForm.step);
      syncSmallQuesProps(['decimal']);
    }

    /* 改变作文格字数更新id,删除高度,强制刷新页面组件 */
    const changeWordNumber = () => {
      state.baseForm.id = generateUUID();
      state.baseForm.data.forEach(item => {
        if (item.height) delete item.height;
        let uid = generateUUID();
        if (item.isChooseDo) {
          item.chooseIds.forEach((id, i) => {
            if (id === item.id) {
              item.chooseIds[i] = uid;
            }
          });
          item.targetIds.forEach((id, i) => {
            if (id === item.id) {
              item.targetIds[i] = uid;
            }
          });
        }
        item.id = uid;
      });
    };

    /**
     * @description: 切换作文格尺寸
     * @return {*}
     */
    const changeCellSize = () => {
      state.baseForm.data.forEach(item => (item.cellSize = state.baseForm.cellSize));
    };

    /* 切换打分类型 */
    const changeMakeType = (val: number) => {
      if (val == MARK_TYPE.YESORNO) {
        state.baseForm.judgeType = JUDGE_TYPE.MARK;
      } else {
        state.baseForm.judgeType = val;
      }
      syncSmallQuesProps(['markType', 'judgeType']);
    };

    const initData = () => {
      state.baseForm.mixinMode = false;
      if (props.mode == 'add') {
        // 添加模式
        state.baseForm.id = generateUUID();
        if (Paper.getBigQuesLength() != 0) {
          state.lastSmallQuesIndex = Paper.getSmallQuesLength();
        } else {
          state.lastSmallQuesIndex = 0;
        }
        state.baseForm.startNo = state.lastSmallQuesIndex + 1;
        state.baseForm.count = 1;
        const quesName = getQuesName();
        state.baseForm.type = quesName;
        state.baseForm.name = arabToChinese(Number(Paper.getBigQuesLength() + 1)) + '、' + quesName;
        state.baseForm.typeId = props.quesType;
        if (Paper.cardType == ICARDMODEL.BLANKCARD || Paper.cardType == ICARDMODEL.PHOTO) {
          state.samllModel = getSmallQuesModel();
          state.baseForm.quesName = state.samllModel.quesNos;
        }
        handleSmallQues();
        state.baseForm.data = [state.samllModel];
      } else {
        // 编辑模式，抓取扁平列表中的混合题，标记parentId
        state.baseForm = deepClone(Paper.quesInfos[props.index]);

        if (state.baseForm.mixinMode) {
          let quesList = deepClone(state.baseForm.data);
          let baseFormData = [];
          // 混合模式题型，补充children信息
          quesList.forEach(item => {
            if (item.parentId) return;

            if (item.mixinMode) {
              // 混合模式题型
              let id = item.id;
              item.children = quesList.filter(item => item.parentId == id);

              baseFormData.push(item);
              return;
            }

            baseFormData.push(item);
          });
          state.baseForm.data = baseFormData;
          // console.log('state.baseForm.data:', state.baseForm.data);
        } else if (Paper.cardType == ICARDMODEL.BLANKCARD || Paper.cardType == ICARDMODEL.PHOTO) {
          state.baseForm.id = generateUUID();
          // 检测作文题是否包含卷面分，防止出现无卷面分但状态未重置的状态
          let faceQues = state.baseForm.data.find(item => item.typeMode == SUBJECT_MODE.face);
          if (!faceQues) state.baseForm.hasFaceScore = false;
        }
        if (Number(state.baseForm.quesScore) == 0) state.baseForm.quesScore = 5;
        if (state.baseForm.hideQuesSurface == null) state.baseForm.hideQuesSurface = '0';
        if (state.baseForm.hideSmallQues == null) state.baseForm.hideSmallQues = '0';

        console.log('state.baseForm: ', state.baseForm);
      }

      state.dataLoading = false;
    };

    initData();

    return {
      ...toRefs(state),
      formRef,
      formTable,
      showNameRulesInput,
      isAnsToTop,
      isShowChooseDoQuesBtn,
      isPhotoCorrect,
      checkStartNo,
      checkEndNo,
      setQuesNum,
      closeDialog,
      addSmallQues,
      confirmSubmit,
      changeLineNum,
      checkIsStepRule,
      deleteSmallQues,
      onChangeQuesName,
      changeOptionCount,
      caculTotalScoreChildren,
      changeOptionEvent,
      changeHalfScoreEvent,
      changeRuleTypeEvent,
      changeScore,
      changeQuesScore,
      updateRules,
      updateChooseDo,
      indexMethod,
      changeQuesNum,
      toggleFaceQues,
      changeMakeType,
      changeFillType,
      changeScoreEvent,
      changeWordNumber,
      changeDecimalType,
      changeCellSize,
      changeLineWidth,
      syncSmallQuesProps,
      convertQuesName,
    };
  },
});
</script>

<style lang="scss" scoped>
.c30-ques-add-modal {
  .from-base-col {
    padding: 0 10px 0 10px !important;
    border-right: 1px dotted #d8d8d8;
  }

  .from-base-wrap {
    padding-top: 15px;

    .set-score-rule {
      color: rgb(64, 158, 255);
      line-height: 14px;
      cursor: pointer;
    }
  }

  .el-dialog__headerbtn {
    right: 30px;
  }

  .el-input-number {
    width: 140px !important;
  }

  .el-radio {
    margin-right: 10px;
  }

  .sort-input {
    width: 60px !important;
  }

  .el-form-item {
    margin-bottom: 17px !important;
  }

  .mark-type-group {
    white-space: nowrap;

    ::v-deep .el-radio-button__inner {
      padding: {
        left: 10px;
        right: 10px;
      }
    }
  }

  ::v-deep {
    .el-table__indent {
      display: none;
    }
  }
}
</style>

<style lang="scss">
.c30-ques-add-modal {
  .el-dialog__header {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .el-dialog__body {
    border-bottom: 1px solid #d8d8d8 !important;
  }

  .from-base-wrap {
    .el-form-item__content {
      color: #000 !important;
      line-height: normal;
      white-space: nowrap;

      .el-radio {
        margin-right: 3px;

        .el-radio__label {
          padding-left: 3px;
        }
      }
    }
  }

  .form-table {
    padding: 0;

    .el-form-item__content {
      margin-left: 20px !important;
      line-height: 30px !important;
    }

    .el-input__icon {
      line-height: 30px !important;
    }

    .el-table__expand-icon {
      margin-left: -23px;
    }

    .el-input {
      &.sort-input {
        width: 70px;
      }

      .el-input__inner {
        text-align: center;
        appearance: none;
      }
    }

    .el-table__row--level-1 {
      background-color: #f7f7f7b6;
    }
  }

  .el-col {
    height: 450px;
  }

  .el-dialog__footer {
    padding: 14px !important;
  }

  .el-table {
    min-height: 480px;
    color: #000 !important;

    &::before {
      display: none;
    }
  }

  .el-table thead {
    color: #878787 !important;
  }

  .el-input__inner {
    color: #000 !important;
    height: 30px !important;
    line-height: 30px !important;
  }

  .el-input-number .el-input__inner {
    height: 32px !important;
    line-height: 32px !important;
  }
}
</style>