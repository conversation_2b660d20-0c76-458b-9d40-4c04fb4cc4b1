<!--
 * @Description: 拍改模式模板
 * @Author: qmzhang
 * @Date: 2024-11-19 11:01:56
 * @LastEditTime: 2025-09-04 15:55:39
 * @FilePath: \make-card\src\views\cardPhotoMain.vue
-->
<template>
    <div class="page-box page-box--ques noselect clearfix text-center" :style='{
        "--fontSize": data.fontSize,
        "--fontFamily": data.fontFamily,
        "--lineHeight": data.space,
    }'>
        <div class="header-box"></div>
        <br>
        <PageHeaderTitle />
        <div class="q-opt photo-correct-area clearfix" :style="{ maxWidth: boxMaxWidth + 'mm' }">
            <StuNumberFillCard optionBlock="rect" v-if="paper.numberLayout == inumberlayout.TICKET">
                <template #table-header>
                    <tr class="table-caption">
                        <th :colspan="paper.stuNoLength">姓名：</th>
                    </tr>
                </template>
            </StuNumberFillCard>

            <div class="stu-bar-code-container pull-right" id="stu-no-three-tr" v-else>
                <div class="stu-bar-code">
                    <span></span>
                    <div class="text text--top gray"><span>贴条形码区</span></div>
                    <div class="text text--bottom gray">
                        <span>(正面朝上，切勿贴出虚线方框)</span>
                    </div>
                    <span></span>
                </div>
            </div>

            <template v-for="(subItem, index) in quesList">
                <judge-view class="pull-left choice-item--rect" optionBlock="rect" :index="index" :subItem="subItem"
                    @setAnswer="tapTFButton" v-if="subItem.typeId == quesType.judge" />
                <choice-view class="pull-left choice-item--rect" optionBlock="rect" :index="index" :subItem="subItem"
                    @setAnswer="setAnswer" :max-length="subItem.optionCount > 4 ? 9 : 4" v-else />
            </template>
            <div class="br-block"></div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import {
    ICARDMODEL, ICorrectType, QUES_TYPE, INUMBERLAYOUT
} from '@/typings/card';
import Paper from "../views/paper";
import { onMounted, onActivated, ref, Ref, computed, onBeforeUnmount } from 'vue';
import StuNumberFillCard from "../components/stuNumberFillCard.vue";
import ChoiceView from '../components/empty/QuesCardObject/Choice.view.vue';
import JudgeView from '../components/empty/QuesCardObject/Judge.view.vue';
import PageHeaderTitle from '@/components/PageHeaderTitle.vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {};
        },
    },
})

const paper = ref(Paper);
const quesType = ref(QUES_TYPE);
const inumberlayout = ref(INUMBERLAYOUT);
const quesInfos = ref(Paper.quesInfosInCard);

const quesLength = computed(() => {
    return quesInfos.value.length ? quesInfos.value[0].data.length : 0
})
const quesList = computed(() => {
    return quesInfos.value.length ? quesInfos.value[0].data : []
})

// 根据题目数量自动计算盒子的最大宽度mm
const boxMaxWidth = computed(() => {
    const maxQuesRows = 11;
    if (paper.value.numberLayout == INUMBERLAYOUT.TICKET) {
        if (quesLength.value <= maxQuesRows) {
            return 105 + (paper.value.stuNoLength - 8) * 7
        } else if (quesLength.value <= maxQuesRows * 2) {
            return 148
        } else {
            return 180
        }
    } else {
        if (quesLength.value <= maxQuesRows) {
            return 110
        } else {
            return 120
        }
    }
})

/**
 * @name 设置选择题答案
 * @param item :当前题
 * @param index:选项
 */
const setAnswer = (item: any, index: any) => {
    const az = "0ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    if (item.typeId == QUES_TYPE.choice) {
        // 多选题
        item.answer = item.answer ? item.answer.split(',') : [];
        if (item.answer.includes(az[index])) {
            item.answer = item.answer.filter((ite: any) => {
                return ite != az[index];
            });
        } else {
            item.answer.push(az[index]);
        }
        item.answer = item.answer.join(',');
    } else {
        // 单选题
        item.answer = az[index];
    }

    Paper.notifyUpdateData('card');
};

/* 点击判断题按钮 */
const tapTFButton = (item: any, answer: string) => {
    item.answer = answer;
    // TIP: 这里需要手动同步客观题答案，兼容题卡合一混合题导致数据隔离无法同步的情况
    syncSettingToOrigin(item.id, item.id, 'answer', item.answer);
    Paper.notifyUpdateData('card');
};

/* 同步设置到源数据 */
const syncSettingToOrigin = (qId: string, sqId: string, key: string, value: any) => {
    // if (Paper.cardType == ICARDMODEL.BLANKCARD && Paper.mergeQues == 0) return
    //增加保底策略，如果大题id和小题id查找不到对应源数据，则通过大题id查找小题源数据
    let smallQues: any = Paper.findSmallQuesByQuesId(qId)
        || Paper.findBigQuesByQuesId(qId)
        || Paper.findBigQuesBySmallQuesId(sqId)
        || Paper.findSmallQuesByQuesId(sqId)

    if (Paper.cardType == ICARDMODEL.ONLYCARD && smallQues) {
        let isMixin = Paper.checkQuesIsMixin(smallQues);
        if (isMixin) smallQues = Paper.findSmallQuesByQuesId(sqId)
    }
    if (!smallQues) return

    smallQues[key] = value;
};

const handleUpdateQustions = (from) => {
    if (from !== 'right') return;

    quesInfos.value = Paper.quesInfosInCard;
}
const handleUpdateAllQustions = () => {
    quesInfos.value = Paper.quesInfosInCard;
}

Paper.on('updateQustions', handleUpdateQustions);
Paper.on('updateAllQustions', handleUpdateAllQustions);

onMounted(() => {

})
onBeforeUnmount(() => {
    Paper.off('updateQustions', handleUpdateQustions);
    Paper.off('updateAllQustions', handleUpdateAllQustions);
})

</script>


<style lang="scss" scoped>
.page-box {
    width: 100%;
}

.photo-correct-area {
    display: inline-block;
    width: auto;
    min-width: 100mm;
    margin: 6mm auto 0;
    padding-left: 2mm;
    font-family: "Times New Roman";
    border: 3px solid #000;
    border-radius: 1px;

    ::v-deep {
        .stu-no-table {
            float: right;
            height: 287px;
            border-spacing: 4px;
            margin-left: 10px;
        }

        .stu-num-full-sec {
            width: 100%;
            height: 4.7mm;
            border: 0.4mm solid #000 !important;
            color: #666;
        }

        .ques-sort {
            width: 7mm;
            text-align: right;
            padding-right: 3px;
        }
    }

    .choice-item--rect {
        margin: 2.2mm 0.2mm 0;

        ::v-deep {
            .option-item {
                margin: 0 0.8mm;
                line-height: 4mm;
                height: 4.7mm;
                border: 0.4mm solid #000;
                font-family: "Times New Roman";
                color: #666;
            }
        }
    }

    .br-block {
        float: none;
        clear: both;
        height: 4mm;
    }
}

.table-caption th {
    padding: 0 5px;
    font-size: 12px;
}

.stu-bar-code-container {
    width: 65mm;
    height: 30mm;
    padding: 5mm;
    border-left: 1px solid #000;
    border-bottom: 1px solid #000;
    margin-left: 2mm;
    margin-bottom: 4.5mm;
}

.stu-bar-code {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;
    width: 100%;
    height: 100%;
    background-color: #e4e4e4;

    .text {
        color: #a5a5a5;
    }

    .text--top {
        font-size: 20px;
    }

    .text--bottom {
        font-size: 12px;
    }
}
</style>