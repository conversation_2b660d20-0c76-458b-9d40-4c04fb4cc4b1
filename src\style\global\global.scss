@import '../../assets/font/iconfont.css';
@import './card-zoom';
@import './edit-tools';

#app {
  width: 100%;
  height: 100%;
  overflow: auto;
  // -webkit-user-select: none;
  // -moz-user-select: none;
  // -ms-user-select: none;
  // user-select: none;
}

.page-box--ques .score-table {
  border: 0.1mm solid #000;
}

.nameset .el-select .el-popper {
  inset: unset !important;

  .el-popper__arrow {
    display: none;
  }
}

.q-opt {
  width: 100%;
  position: relative;

  &:hover {
    z-index: 100 !important;

    .edit,
    .writing-line {
      display: block;
    }
  }

  &.is-small-mixin {
    +.is-small-mixin {
      .ques-box-container {
        border-top: none;
      }
    }
  }

  &.layout-a33 {
    &.is-objective {
      .ques-item-wrap {
        left: 0;
      }
    }

    .ques-item-wrap {
      .choice-item {
        margin-right: 0;

        .ques-sort {
          width: 6mm;
        }
      }
    }
  }

  .fill-item {
    .ques-sort {
      line-height: normal;
      padding-left: 0;
      text-align: right;
      min-height: 6mm;
    }
  }

  &.web {
    .fill-item {

      .fill-container,
      .score-opts-list {
        display: flex;
        justify-content: center;
      }

      .fill-container,
      .score-opts-list {
        align-items: end;
      }
    }

    .split-line {
      background-color: #fff;

      &.split-line--subject {
        &.hide-block::after {
          visibility: hidden;
        }
      }
    }
  }

  &.hand {
    .split-line {
      &.split-line--subject {
        &.hide-block {
          display: none;
        }
      }
    }

    .score-opts-list {
      // padding-top: 3mm;
      vertical-align: top;
    }
  }

  &+.q-opt {

    &.ques-noanswerarea,
    .ques-box-container {

      .ques-box,
      .score-table,
      &::after {
        border-top-color: transparent !important;
      }
    }

    .ques-content-name+.splitnode-container .ques-box-container,
    .page-splitor+.ques-box-container {

      &::after,
      .score-table {
        border-top-color: #000 !important;
      }
    }
  }

  .line {
    display: inline-block;
    cursor: pointer;
    width: 24px;
    height: 24px;
    right: -4px;
    background: url('../../assets/icon-line.png') no-repeat;
    background-size: 150%;
    background-position: center center;
    z-index: 999;
  }

  .up {
    right: 50px;
    top: 0;
  }
}
.quescard{
  .q-item-box8,.q-item-box1,.q-item-box2,.q-item-box3{
    &+.q-opt {

      &.ques-noanswerarea,
      .ques-box-container {
  
        .ques-box,
        .score-table,
        &::after {
          border-top-color: #000 !important;
        }
      }
  
      .ques-content-name+.splitnode-container .ques-box-container,
      .page-splitor+.ques-box-container {
  
        &::after,
        .score-table {
          border-top-color: #000 !important;
        }
      }
    }
  }
}

.q-opt.is-big-mixin+.q-opt .ques-box-container .score-table,
.q-opt.is-big-mixin+.q-opt .ques-box-container::after {
  border-top-color: #000 !important;
}

.q-item-box6+.q-item-box6 .score-table,
.q-item-box6:not(.split-ques)+.q-item-box6 .ques-box,
.no-top+.ques-box {
  border-top-color: transparent !important;
}

.score-container {
  display: block;
}

.align-left {
  display: block !important;
  text-align: left;
}

.align-right {
  display: block !important;
  text-align: right;
}

.align-center {
  display: block !important;
  text-align: center;
}

.dis-none {
  display: none;
}

::selection {
  background: #227afeb7 !important;
}

img::selection {
  background: none !important;
}

.q-opt {
  &.card {
    .ques-box {
      position: relative;
      padding: 1mm 0 4mm;

      &.subject-box {
        padding: unset;
        position: relative;
        overflow: hidden;
        text-align: center;
      }

      &.no-items {
        padding: 0;

        .ques-item-wrap,
        .ques-box {
          padding: 0;
        }
      }
    }
  }

  .ques-box-container {
    position: relative;

    &::after {
      content: '';
      pointer-events: none;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
      border: 0.1mm solid #000;
    }
  }

  .ques-box-wrap {
    position: relative;

    &:hover {

      .pop,
      .edit-button-tools {
        display: block;
      }
    }


    &:active .pop {
      display: block;
    }

    &.no-items {
      padding: 0;

      .ques-box {
        padding: 0;
      }
    }
  }

  &.is-objective {
    .ques-item-wrap {
      position: relative;
      // 这里偏移三个单位保持和客观题对齐
      left: 3mm;
    }
  }

  .ques-item-wrap {
    display: inline-block;
    margin: 1mm 0;
    // vertical-align: bottom;

    &.vertical {
      display: inline-flex;
      flex-wrap: wrap;
      flex-direction: column;
      max-height: 30mm;
    }

    .choice-item {
      margin-right: 5mm;

      .ques-sort {
        padding-left: 0;
        padding-right: 0.3mm;
        text-align: right;
        // transform: translateY(9%);
        overflow: hidden;
      }
    }
  }


  .split-line {
    position: relative;
    display: block;
    padding: 1mm 0;
    cursor: pointer;

    &.split-line--linefeed {
      visibility: hidden;
      padding: 0 !important;
      height: 0;
    }

    &.split-line--subject {
      position: absolute;
      margin: -1mm 0;
      z-index: 100;
      left: 0;
      width: 100%;
      box-sizing: border-box;

      border: {
        left: 0.1mm solid;
        right: 0.1mm solid;
      }

      &.hide-block {
        pointer-events: none;
      }
    }

    &:hover {
      background-color: #ccc !important;
    }

    &::after {
      content: '';
      display: block;
      width: 100%;
      height: 0.1mm;
      border-bottom: 0.1mm solid;
    }
  }

  .ques-item {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    position: relative;
    line-height: 4.7mm;
    font-family: 宋体;

    .fill-item {
      display: flex;
      flex-direction: row;
    }
  }

  .ques-sort {
    display: inline-block;
    text-align: center;
    font-size: 3.2mm;
    width: 7mm;
    white-space: nowrap;

    &.hidden {
      visibility: hidden;
    }
  }

  .ques-sort-hidden {
    visibility: hidden;
  }

  .ques-score {
    display: inline-block;
  }

  .subject-ques-sort {
    display: block;
  }

  .dotDot {
    border-bottom: 1px solid #000;
  }


  .option-item {
    display: inline-block;
    width: 6mm;
    margin: 0 0.2mm;
    font-size: 2.6mm;
    border-radius: 0.3mm;
    text-align: center;
    white-space: nowrap;

    &:hover {
      background-color: #3399ff25;
    }

    &.full-color {
      background-color: #3399ff;
      color: #fff !important;
    }

    &.option-item--fill {
      visibility: hidden;
    }
  }

  .fill-container {
    vertical-align: bottom;
    margin: 0;
    padding: 0;
    min-height: 9.8mm;
    border-bottom: 0.1mm solid #000;
    display: inline-block;
    overflow: hidden;
    font-size: 3.6mm;
  }

  .writing-container {
    &:last-child {
      padding-bottom: 3mm;
    }

    &:empty {
      padding: 0 !important;
    }
  }


  .opttion-txt {
    vertical-align: middle;
    padding: 0 0.6mm;
  }

  .full-color {
    background-color: #3399ff;
  }

  .writing-cell {
    margin: 0;
    padding: 0;
    border: 1px solid #999;
    display: inline-block;
    vertical-align: bottom;
    position: relative;
    font-size: 4.2mm;
    border-right: 0px;


    .writing-mark {
      font-size: 12px;
      transform: scale(0.7);
      height: 1mm;
      word-break: keep-all;
      position: absolute;
      bottom: 1px;
      left: -4px;
    }

    &:last-child {
      border-right: 1px solid #999 !important;
    }
  }

  .ques-content-name--small {
    padding: 0 3mm;
    line-height: 7mm;

    &:empty {
      display: none;
    }
  }

  .water-mark {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    width: 200px;
    height: 50px;
    line-height: 50px;
    color: #cccccc;
    font-size: 20px;
    font-weight: 800;
    text-align: center;
    opacity: 0.3;
    user-select: none;
    pointer-events: none;
  }


  .score-opts-list {
    line-height: normal;
    display: inline-block;
    user-select: none;
  }

  .subject-para-p u {
    word-break: break-all;
  }

  .subject-box {
    &.is-wrigting {
      &[data-index='0'] {
        .subject-para-p {
          display: block;
        }
      }

      .subject-para-p {
        display: none;

        +p {
          display: none;
        }
      }
    }

    span.remove-ques {
      +p.remove-ques {
        display: none;
      }

      +.writing-container {
        padding-top: 0;
      }
    }
  }
}

.imageResize {
  background: {
    image: url('../../assets/icon_resize.svg');
    size: cover;
    repeat: no-repeat;
    color: #ffffffcb;
  }

  &:active {
    opacity: 0;
  }
}